{"@@locale": "ja", "friendlyName": "日本語", "localeTitle": "ロケール", "defaultLocale": "デフォルトロケール", "loading": "読み込み中...", "loadModel": "モデルを読み込む", "downloadModel": "モデルをダウンロード", "noModelSelected": "モデルが選択されていません", "noModelLoaded": "モデルが読み込まれていません", "localModels": "ローカルモデル", "size": "サイズ", "parameters": "パラメータ", "delete": "削除", "select": "選択", "import": "インポート", "export": "エクスポート", "edit": "編集", "regenerate": "再生成", "chatsTitle": "チャット", "newChat": "新しいチャット", "anErrorOccurred": "エラーが発生しました", "errorTitle": "エラー", "key": "キー", "value": "値", "ok": "OK", "proceed": "続行", "done": "完了", "close": "閉じる", "save": "保存", "saveLabel": "{label} を保存", "selectTag": "タグを選択", "next": "次へ", "previous": "前へ", "contentShared": "コンテンツが共有されました", "setUserImage": "ユーザー画像を設定", "setAssistantImage": "アシスタント画像を設定", "loadUserImage": "ユーザー画像を読み込む", "loadAssistantImage": "アシスタント画像を読み込む", "userName": "ユーザー名", "assistantName": "アシスタント名", "user": "ユーザー", "assistant": "アシスタント", "cancel": "キャンセル", "aiEcosystem": "AIエコシステム", "llamaCpp": "Llama CPP", "llamaCppModel": "Llama CPPモデル", "remoteModel": "リモートモデル", "refreshRemoteModels": "リモートモデルを更新", "ollama": "Ollama", "searchLocalNetwork": "ローカルネットワークを検索", "localNetworkSearchTitle": "ローカルネットワーク検索", "localNetworkSearchContent": "この機能は、ローカルネットワーク上のOllamaインスタンスを検索するための追加の権限を必要とします。", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "モデルパラメータ", "addParameter": "パラメータを追加", "removeParameter": "パラメータを削除", "saveParameters": "パラメータを保存", "importParameters": "パラメータをインポート", "exportParameters": "パラメータをエクスポート", "selectAiEcosystem": "AIエコシステムを選択", "selectRemoteModel": "リモートモデルを選択", "selectThemeMode": "アプリのテーマモードを選択", "themeMode": "テーマモード", "themeModeSystem": "システム", "themeModeLight": "ライト", "themeModeDark": "ダーク", "themeSeedColor": "テーマの基調色", "editMessage": "メッセージを編集", "settingsTitle": "設定", "aiSettings": "{aiType} の設定", "userSettings": "ユーザー設定", "assistantSettings": "アシスタント設定", "systemSettings": "システム設定", "systemPrompt": "システムプロンプト", "clearChats": "チャットをクリア", "resetSettings": "設定をリセット", "clearCache": "キャッシュをクリア", "aboutTitle": "アプリについて", "aboutContent": "Maidは、ローカルのllama.cppモデル、およびリモートのOllama、Mistral、Google Gemini、OpenAIモデルとインターフェースするためのクロスプラットフォームの無料かつオープンソースのアプリケーションです。Maidは、SillyTavernのキャラクターカードをサポートしており、お気に入りのキャラクターと対話することができます。Maidは、Hugging Faceからモデルを直接アプリ内でダウンロードできる機能を提供しています。MaidはMITライセンスのもとで配布されており、明示的または暗示的な保証なしで提供されます。Maidは、Hugging Face、Meta (Facebook)、MistralAI、OpenAI、Google、Microsoft、またはこのアプリケーションと互換性のあるモデルを提供する他の企業とは提携していません。", "leadMaintainer": "リードメンテナー", "apiKey": "APIキー", "baseUrl": "ベースURL", "scrollToRecent": "最近のメッセージにスクロール", "clearPrompt": "プロンプトをクリア", "submitPrompt": "プロンプトを送信", "stopPrompt": "プロンプトを停止", "typeMessage": "メッセージを入力...", "code": "コード", "copyLabel": "{label} をコピー", "labelCopied": "{label} をクリップボードにコピーしました！", "debugTitle": "デバッグ", "warning": "警告", "nsfwWarning": "このモデルは、意図的にNSFW（閲覧注意）コンテンツを生成するように設計されています。これには、拷問、レイプ、殺人、および／または性的に逸脱した行為を含む、露骨な性的または暴力的なコンテンツが含まれる場合があります。こうした内容に敏感な方、またはその議論が現地の法律に違反する場合は、先に進まないでください。", "login": "ログイン", "logout": "ログアウト", "register": "登録する", "email": "メールアドレス", "password": "パスワード", "confirmPassword": "パスワードの確認", "resetCode": "リセットコード", "resetCodeSent": "リセットコードがメールに送信されました。", "sendResetCode": "リセットコードを送信", "sendAgain": "再送信", "required": "必須", "invalidEmail": "有効なメールアドレスを入力してください", "invalidUserName": "3～24文字、英数字またはアンダースコアである必要があります", "invalidPasswordLength": "最低8文字", "invalidPassword": "大文字、小文字、数字、記号を含めてください", "passwordNoMatch": "パスワードが一致しません", "createAccount": "アカウントを作成", "resetPassword": "パスワードをリセット", "backToLogin": "ログインに戻る", "alreadyHaveAccount": "既にアカウントをお持ちですか", "success": "成功", "registrationSuccess": "登録に成功しました", "resetSuccess": "パスワードが正常にリセットされました。", "emailVerify": "確認のため、メールをチェックしてください。"}