{"@@locale": "en", "friendlyName": "English", "localeTitle": "Locale", "defaultLocale": "Default Locale", "loading": "Loading...", "loadModel": "Load Model", "downloadModel": "Download Model", "noModelSelected": "No Model Selected", "noModelLoaded": "No Model Loaded", "localModels": "Local Models", "size": "Size", "parameters": "Parameters", "delete": "Delete", "select": "Select", "import": "Import", "export": "Export", "edit": "Edit", "regenerate": "Regenerate", "chatsTitle": "Chats", "newChat": "New Chat", "anErrorOccurred": "An error occurred", "errorTitle": "Error", "key": "Key", "value": "Value", "ok": "OK", "proceed": "Proceed", "done": "Done", "close": "Close", "save": "Save", "saveLabel": "Save {label}", "@saveLabel": {"description": "Save the {label}", "placeholders": {"label": {"type": "String", "example": "Message"}}}, "selectTag": "Select Tag", "next": "Next", "previous": "Previous", "contentShared": "Content Shared", "setUserImage": "Set User Image", "setAssistantImage": "Set Assistant Image", "loadUserImage": "Load User Image", "loadAssistantImage": "Load Assistant Image", "userName": "User Name", "assistantName": "Assistant Name", "user": "User", "assistant": "Assistant", "cancel": "Cancel", "aiEcosystem": "AI Ecosystem", "llamaCpp": "Llama CPP", "llamaCppModel": "Llama CPP Model", "remoteModel": "Remote Model", "refreshRemoteModels": "Refresh Remote Models", "ollama": "Ollama", "searchLocalNetwork": "Search Local Network", "localNetworkSearchTitle": "Local Network Search", "localNetworkSearchContent": "This feature requires additional permissions to search your local network for Ollama instances.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Model Parameters", "addParameter": "Add Parameter", "removeParameter": "Remove Parameter", "saveParameters": "Save Parameters", "importParameters": "Import Parameters", "exportParameters": "Export Parameters", "selectAiEcosystem": "Select AI Ecosystem", "selectRemoteModel": "Select Remote Model", "selectThemeMode": "Select App Theme Mode", "themeMode": "Theme Mode", "themeModeSystem": "System", "themeModeLight": "Light", "themeModeDark": "Dark", "themeSeedColor": "Theme Seed Color", "editMessage": "Edit Message", "settingsTitle": "Settings", "aiSettings": "{aiType} Settings", "@aiSettings": {"description": "Settings for {aiType}", "placeholders": {"aiType": {"type": "String", "example": "Llama CPP"}}}, "userSettings": "User Settings", "assistantSettings": "Assistant Settings", "systemSettings": "System Settings", "systemPrompt": "System Prompt", "clearChats": "Clear Chats", "resetSettings": "Reset Settings", "clearCache": "<PERSON>ache", "aboutTitle": "About", "aboutContent": "Maid is a cross-platform free and open source application for interfacing with llama.cpp models locally, and remotely with Ollama, Mistral, and OpenAI models remotely. <PERSON> supports sillytavern character cards to allow you to interact with all your favorite characters. <PERSON> supports downloading a curated list of Models in-app directly from hugging<PERSON>. Maid is distributed under the MIT licence and is provided without warrenty of any kind, express or implied. <PERSON> is not affiliated with Huggingface, Meta (Facebook), MistralAi, OpenAI, Google, Microsoft or any other company providing a model compatible with this application.", "leadMaintainer": "Lead Maintainer", "apiKey": "API Key", "baseUrl": "Base URL", "scrollToRecent": "Scroll to Recent", "clearPrompt": "Clear Prompt", "submitPrompt": "Submit Prompt", "stopPrompt": "Stop Prompt", "typeMessage": "Type a message...", "code": "Code", "copyLabel": "Copy {label}", "@copyLabel": {"description": "Copy the {label} to the clipboard", "placeholders": {"label": {"type": "String", "example": "Code"}}}, "labelCopied": "{label} copied to clipboard!", "@labelCopied": {"description": "{label} copied to clipboard!", "placeholders": {"label": {"type": "String", "example": "Code"}}}, "debugTitle": "Debug", "warning": "Warning", "nsfwWarning": "This model is intentionally designed to produce NSFW content. This may include explicit sexual or violent content involving torture, rape, murder and / or sexually deviant behaviour. If you are sensitive to such topics, or the discussion of such topics violates local laws, DO NOT PROCEED.", "login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "resetCode": "Reset Code", "resetCodeSent": "A reset code has been sent to your email.", "sendResetCode": "Send Reset Code", "sendAgain": "Send Again", "required": "Required", "invalidEmail": "Please enter a valid email", "invalidUserName": "Must be 3-24 characters, alphanumeric or underscore", "invalidPasswordLength": "Minimum 8 characters", "invalidPassword": "Include upper, lower, number, and symbol", "passwordNoMatch": "Passwords do not match", "createAccount": "Create an account", "resetPassword": "Reset password", "backToLogin": "Back to Login", "alreadyHaveAccount": "I already have an account", "success": "Success", "registrationSuccess": "Registration Successful", "resetSuccess": "Your password has been reset successfully.", "emailVerify": "Please check your email for verification."}