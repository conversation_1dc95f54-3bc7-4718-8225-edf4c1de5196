name: Release

on:
  release:
    types: [published]

permissions:
  actions: read
  contents: write

jobs:
  build-android:
    uses: ./.github/workflows/build-android.yml
    secrets: inherit

  build-ios:
    uses: ./.github/workflows/build-ios.yml
    secrets: inherit

  build-linux:
    uses: ./.github/workflows/build-linux.yml
    secrets: inherit

  build-macos:
    uses: ./.github/workflows/build-macos.yml
    secrets: inherit

  build-web:
    uses: ./.github/workflows/build-web.yml
    secrets: inherit

  build-windows:
    uses: ./.github/workflows/build-windows.yml
    secrets: inherit

  upload-artifacts:
    runs-on: ubuntu-latest
    needs:
      - build-android
      - build-ios
      - build-linux
      - build-macos
      - build-web
      - build-windows

    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: all-artifacts

      - name: List artifacts
        run: ls -R all-artifacts

      - name: Prepare upload folder
        run: mkdir upload

      - name: Copy Android release files
        run: cp all-artifacts/maid-android/* upload/

      - name: Copy iOS release files
        run: cp all-artifacts/maid-ios/* upload/

      - name: Copy AppImage file
        run: cp all-artifacts/maid-appimage/* upload/

      - name: Copy macOS release files
        run: |
          cp all-artifacts/maid-macos-arm64/* upload/
          cp all-artifacts/maid-macos-x86_64/* upload/

      - name: Zip Linux, Windows, and Web builds (contents only)
        run: |
          cd all-artifacts
    
          # Zip contents of maid-linux
          cd maid-linux
          zip -r ../../upload/maid-linux.zip .
          cd ..
    
          # Zip contents of maid-windows
          cd maid-windows
          zip -r ../../upload/maid-windows.zip .
          cd ..
    
          # Zip contents of maid-web
          cd maid-web
          zip -r ../../upload/maid-web.zip .
          cd ..

      - name: List files for upload
        run: ls -lh upload

      - name: Upload release assets
        uses: softprops/action-gh-release@v1
        with:
          files: upload/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    