    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Global variables
        const API_BASE = 'api/';
        
        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainHeader = document.getElementById('mainHeader');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainHeader.classList.toggle('expanded');
                mainContent.classList.toggle('expanded');
            }
        });
        
        // Close sidebar on mobile when clicking outside
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // Responsive sidebar
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const mainHeader = document.getElementById('mainHeader');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('collapsed');
                sidebar.classList.remove('show');
                mainHeader.classList.add('expanded');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.remove('show');
                if (!sidebar.classList.contains('collapsed')) {
                    mainHeader.classList.remove('expanded');
                    mainContent.classList.remove('expanded');
                }
            }
        });
        
        // API Helper functions
        async function apiRequest(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            const config = { ...defaultOptions, ...options };
            
            try {
                const response = await fetch(API_BASE + endpoint, config);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Erro na requisição');
                }
                
                return data;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }
        
        // Show loading state
        function showLoading(element) {
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            if (element) {
                element.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i>Carregando...</div>';
            }
        }
        
        // Show error message
        function showError(message, container = null) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            if (container) {
                if (typeof container === 'string') {
                    container = document.querySelector(container);
                }
                container.insertBefore(alert, container.firstChild);
            } else {
                document.querySelector('.main-content').insertBefore(alert, document.querySelector('.main-content').firstChild);
            }
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
        
        // Show success message
        function showSuccess(message, container = null) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            if (container) {
                if (typeof container === 'string') {
                    container = document.querySelector(container);
                }
                container.insertBefore(alert, container.firstChild);
            } else {
                document.querySelector('.main-content').insertBefore(alert, document.querySelector('.main-content').firstChild);
            }
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
        
        // Format currency
        function formatCurrency(value) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(value);
        }
        
        // Format date
        function formatDate(dateString, options = {}) {
            const defaultOptions = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            };
            
            const config = { ...defaultOptions, ...options };
            
            return new Intl.DateTimeFormat('pt-BR', config).format(new Date(dateString));
        }
        
        // Get order status badge
        function getStatusBadge(status) {
            const statuses = {
                'pending': { text: 'Pendente', class: 'status-pending' },
                'confirmed': { text: 'Confirmado', class: 'status-confirmed' },
                'preparing': { text: 'Em Preparo', class: 'status-preparing' },
                'ready': { text: 'Pronto', class: 'status-ready' },
                'out_for_delivery': { text: 'Saiu para Entrega', class: 'status-ready' },
                'delivered': { text: 'Entregue', class: 'status-delivered' },
                'cancelled': { text: 'Cancelado', class: 'status-cancelled' }
            };
            
            const statusInfo = statuses[status] || { text: status, class: 'status-pending' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
        }
        
        // Initialize DataTables with default options
        function initDataTable(selector, options = {}) {
            const defaultOptions = {
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip'
            };
            
            return $(selector).DataTable({ ...defaultOptions, ...options });
        }
        
        // Update pending orders count
        async function updatePendingOrdersCount() {
            try {
                const response = await apiRequest('orders.php?status=pending&limit=1');
                const count = response.pagination?.total_items || 0;
                
                const badge = document.getElementById('pending-orders-count');
                if (badge) {
                    badge.textContent = count;
                    badge.style.display = count > 0 ? 'inline-block' : 'none';
                }
            } catch (error) {
                console.error('Error updating pending orders count:', error);
            }
        }
        
        // Initialize pending orders count
        updatePendingOrdersCount();
        
        // Update count every 30 seconds
        setInterval(updatePendingOrdersCount, 30000);
        
        // Confirm dialog
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }
        
        // Modal helper
        function showModal(title, content, size = '') {
            const modalId = 'dynamicModal';
            let modal = document.getElementById(modalId);
            
            if (!modal) {
                modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.id = modalId;
                modal.innerHTML = `
                    <div class="modal-dialog ${size}">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body"></div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }
            
            modal.querySelector('.modal-title').textContent = title;
            modal.querySelector('.modal-body').innerHTML = content;
            
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            
            return bsModal;
        }
        
        // Auto-logout on session timeout
        let sessionTimeout;
        
        function resetSessionTimeout() {
            clearTimeout(sessionTimeout);
            sessionTimeout = setTimeout(() => {
                alert('Sua sessão expirou. Você será redirecionado para a página de login.');
                window.location.href = 'login.php';
            }, 120 * 60 * 1000); // 120 minutes
        }
        
        // Reset timeout on user activity
        document.addEventListener('click', resetSessionTimeout);
        document.addEventListener('keypress', resetSessionTimeout);
        resetSessionTimeout();
    </script>
    
    <?php if (isset($additionalJS)): ?>
        <?= $additionalJS ?>
    <?php endif; ?>
</body>
</html>
