<?php
/**
 * API de Configurações - Laricas Delivery Admin
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar autenticação
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Não autenticado'], 401);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Método não permitido'], 405);
    }
} catch (Exception $e) {
    error_log("Settings API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Erro interno do servidor'], 500);
}

function handleGet($db) {
    $group = $_GET['group'] ?? null;
    
    $where = ['1=1'];
    $params = [];
    
    if ($group) {
        $where[] = '`group` = :group';
        $params['group'] = $group;
    }
    
    $whereClause = implode(' AND ', $where);
    
    $sql = "SELECT * FROM settings WHERE {$whereClause} ORDER BY `group`, `key`";
    $settings = $db->fetchAll($sql, $params);
    
    // Organizar por grupo
    $organized = [];
    foreach ($settings as $setting) {
        $value = $setting['value'];
        
        // Converter valor baseado no tipo
        switch ($setting['type']) {
            case 'number':
                $value = is_numeric($value) ? (float)$value : 0;
                break;
            case 'boolean':
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                break;
            case 'json':
                $value = json_decode($value, true) ?: [];
                break;
        }
        
        $organized[$setting['group']][$setting['key']] = [
            'value' => $value,
            'type' => $setting['type'],
            'description' => $setting['description']
        ];
    }
    
    jsonResponse([
        'success' => true,
        'data' => $group ? ($organized[$group] ?? []) : $organized
    ]);
}

function handlePost($db, $input) {
    if (!hasPermission('manage_settings')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    if (empty($input['settings']) || !is_array($input['settings'])) {
        jsonResponse(['success' => false, 'message' => 'Configurações são obrigatórias'], 400);
    }
    
    $db->beginTransaction();
    
    try {
        $updated = 0;
        
        foreach ($input['settings'] as $key => $data) {
            if (!isset($data['value'])) continue;
            
            $value = $data['value'];
            $type = $data['type'] ?? 'string';
            
            // Converter valor baseado no tipo
            switch ($type) {
                case 'json':
                    $value = json_encode($value);
                    break;
                case 'boolean':
                    $value = $value ? 'true' : 'false';
                    break;
                case 'number':
                    $value = (string)$value;
                    break;
            }
            
            // Verificar se configuração existe
            $existing = $db->fetch("SELECT id FROM settings WHERE `key` = :key", ['key' => $key]);
            
            if ($existing) {
                $db->update('settings', 
                    ['value' => $value, 'type' => $type], 
                    '`key` = :key', 
                    ['key' => $key]
                );
            } else {
                $db->insert('settings', [
                    'key' => $key,
                    'value' => $value,
                    'type' => $type,
                    'group' => $data['group'] ?? 'general',
                    'description' => $data['description'] ?? ''
                ]);
            }
            
            $updated++;
        }
        
        $db->commit();
        
        logActivity('settings_updated', 'settings', null, null, $input['settings']);
        
        jsonResponse([
            'success' => true,
            'message' => "Configurações atualizadas com sucesso ({$updated} itens)"
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}
?>
