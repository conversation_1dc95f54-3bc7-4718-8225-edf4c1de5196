<?php
/**
 * API de Cupons - Laricas Delivery Admin
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar autenticação
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Não autenticado'], 401);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        case 'PUT':
            handlePut($db, $input);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Método não permitido'], 405);
    }
} catch (Exception $e) {
    error_log("Coupons API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Erro interno do servidor'], 500);
}

function handleGet($db) {
    $id = $_GET['id'] ?? null;
    $status = $_GET['status'] ?? 'active';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(1, min(100, intval($_GET['limit'] ?? 20)));
    $search = $_GET['search'] ?? '';
    
    if ($id) {
        // Buscar cupom específico
        $coupon = $db->fetch("SELECT * FROM coupons WHERE id = :id", ['id' => $id]);
        
        if (!$coupon) {
            jsonResponse(['success' => false, 'message' => 'Cupom não encontrado'], 404);
        }
        
        $coupon['applicable_categories'] = json_decode($coupon['applicable_categories'] ?? '[]', true) ?: [];
        $coupon['applicable_products'] = json_decode($coupon['applicable_products'] ?? '[]', true) ?: [];
        
        jsonResponse(['success' => true, 'data' => $coupon]);
    } else {
        // Listar cupons
        $offset = ($page - 1) * $limit;
        $where = ['1=1'];
        $params = [];
        
        if ($status !== 'all') {
            $where[] = 'status = :status';
            $params['status'] = $status;
        }
        
        if ($search) {
            $where[] = '(code LIKE :search OR name LIKE :search)';
            $params['search'] = "%{$search}%";
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Contar total
        $countSql = "SELECT COUNT(*) as total FROM coupons WHERE {$whereClause}";
        $total = $db->fetch($countSql, $params)['total'];
        
        // Buscar cupons
        $sql = "
            SELECT *,
                   CASE 
                       WHEN valid_until < NOW() THEN 'expired'
                       WHEN valid_from > NOW() THEN 'scheduled'
                       ELSE status
                   END as current_status
            FROM coupons 
            WHERE {$whereClause}
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $coupons = $db->fetchAll($sql, $params);
        
        $pagination = paginate($total, $limit, $page);
        
        jsonResponse([
            'success' => true,
            'data' => $coupons,
            'pagination' => $pagination
        ]);
    }
}

function handlePost($db, $input) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    // Validar dados obrigatórios
    $required = ['code', 'name', 'type', 'value', 'valid_from', 'valid_until'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            jsonResponse(['success' => false, 'message' => "Campo {$field} é obrigatório"], 400);
        }
    }
    
    // Verificar se código já existe
    $existing = $db->fetch("SELECT id FROM coupons WHERE code = :code", ['code' => $input['code']]);
    if ($existing) {
        jsonResponse(['success' => false, 'message' => 'Código do cupom já existe'], 400);
    }
    
    // Validar tipo e valor
    $allowedTypes = ['percentage', 'fixed', 'free_delivery'];
    if (!in_array($input['type'], $allowedTypes)) {
        jsonResponse(['success' => false, 'message' => 'Tipo de cupom inválido'], 400);
    }
    
    $value = floatval($input['value']);
    if ($input['type'] === 'percentage' && ($value <= 0 || $value > 100)) {
        jsonResponse(['success' => false, 'message' => 'Porcentagem deve estar entre 1 e 100'], 400);
    }
    
    if ($input['type'] === 'fixed' && $value <= 0) {
        jsonResponse(['success' => false, 'message' => 'Valor fixo deve ser maior que zero'], 400);
    }
    
    $couponData = [
        'code' => strtoupper(sanitize($input['code'])),
        'name' => sanitize($input['name']),
        'description' => sanitize($input['description'] ?? ''),
        'type' => $input['type'],
        'value' => $value,
        'minimum_order' => floatval($input['minimum_order'] ?? 0),
        'maximum_discount' => !empty($input['maximum_discount']) ? floatval($input['maximum_discount']) : null,
        'usage_limit' => !empty($input['usage_limit']) ? intval($input['usage_limit']) : null,
        'used_count' => 0,
        'valid_from' => $input['valid_from'],
        'valid_until' => $input['valid_until'],
        'applicable_categories' => !empty($input['applicable_categories']) ? json_encode($input['applicable_categories']) : null,
        'applicable_products' => !empty($input['applicable_products']) ? json_encode($input['applicable_products']) : null,
        'status' => $input['status'] ?? 'active'
    ];
    
    $couponId = $db->insert('coupons', $couponData);
    
    logActivity('coupon_created', 'coupons', $couponId, null, $couponData);
    
    jsonResponse([
        'success' => true,
        'message' => 'Cupom criado com sucesso',
        'data' => ['id' => $couponId]
    ]);
}

function handlePut($db, $input) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID do cupom é obrigatório'], 400);
    }
    
    // Verificar se cupom existe
    $coupon = $db->fetch("SELECT * FROM coupons WHERE id = :id", ['id' => $id]);
    if (!$coupon) {
        jsonResponse(['success' => false, 'message' => 'Cupom não encontrado'], 404);
    }
    
    $updateData = [];
    $allowedFields = [
        'name', 'description', 'type', 'value', 'minimum_order', 'maximum_discount',
        'usage_limit', 'valid_from', 'valid_until', 'applicable_categories',
        'applicable_products', 'status'
    ];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            switch ($field) {
                case 'value':
                case 'minimum_order':
                case 'maximum_discount':
                    $updateData[$field] = !empty($input[$field]) ? floatval($input[$field]) : null;
                    break;
                case 'usage_limit':
                    $updateData[$field] = !empty($input[$field]) ? intval($input[$field]) : null;
                    break;
                case 'applicable_categories':
                case 'applicable_products':
                    $updateData[$field] = !empty($input[$field]) ? json_encode($input[$field]) : null;
                    break;
                default:
                    $updateData[$field] = sanitize($input[$field]);
            }
        }
    }
    
    // Verificar código único se mudou
    if (isset($input['code']) && $input['code'] !== $coupon['code']) {
        $existing = $db->fetch("SELECT id FROM coupons WHERE code = :code AND id != :id", 
                              ['code' => $input['code'], 'id' => $id]);
        if ($existing) {
            jsonResponse(['success' => false, 'message' => 'Código do cupom já existe'], 400);
        }
        $updateData['code'] = strtoupper(sanitize($input['code']));
    }
    
    if (!empty($updateData)) {
        $db->update('coupons', $updateData, 'id = :id', ['id' => $id]);
        
        logActivity('coupon_updated', 'coupons', $id, $coupon, $updateData);
    }
    
    jsonResponse([
        'success' => true,
        'message' => 'Cupom atualizado com sucesso'
    ]);
}

function handleDelete($db) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID do cupom é obrigatório'], 400);
    }
    
    // Verificar se cupom existe
    $coupon = $db->fetch("SELECT * FROM coupons WHERE id = :id", ['id' => $id]);
    if (!$coupon) {
        jsonResponse(['success' => false, 'message' => 'Cupom não encontrado'], 404);
    }
    
    // Se cupom foi usado, apenas inativar
    if ($coupon['used_count'] > 0) {
        $db->update('coupons', ['status' => 'inactive'], 'id = :id', ['id' => $id]);
        $message = 'Cupom inativado (já foi utilizado)';
    } else {
        $db->delete('coupons', 'id = :id', ['id' => $id]);
        $message = 'Cupom deletado com sucesso';
    }
    
    logActivity('coupon_deleted', 'coupons', $id, $coupon, null);
    
    jsonResponse([
        'success' => true,
        'message' => $message
    ]);
}
?>
