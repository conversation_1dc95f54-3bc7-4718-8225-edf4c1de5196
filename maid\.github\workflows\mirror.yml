name: Mirror to GitLab

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  mirror:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
            fetch-depth: 0

      - name: Mirror to GitLab
        run: |
          git config --global user.name 'GitHub Actions'
          git config --global user.email '<EMAIL>'
          git remote add gitlab https://oauth2:${{ secrets.GITLAB_ACCESS_TOKEN }}@gitlab.com/mobile-artificial-intelligence/maid.git
          git push --force gitlab main