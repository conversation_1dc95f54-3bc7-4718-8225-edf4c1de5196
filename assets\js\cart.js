// Shopping Cart Management
class ShoppingCart {
    constructor() {
        this.items = this.loadCart();
        this.updateCartDisplay();
    }

    // Adicionar item ao carrinho
    addItem(itemId, size, quantity = 1) {
        const menuItem = MenuData.getMenuItemById(itemId);
        if (!menuItem) return false;

        const sizeInfo = menuItem.sizes.find(s => s.size === size);
        if (!sizeInfo) return false;

        const cartItemId = `${itemId}-${size}`;
        const existingItem = this.items.find(item => item.id === cartItemId);

        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({
                id: cartItemId,
                itemId: itemId,
                name: menuItem.name,
                description: menuItem.description,
                image: menuItem.image,
                size: size,
                price: sizeInfo.price,
                quantity: quantity,
                category: menuItem.category
            });
        }

        this.saveCart();
        this.updateCartDisplay();
        this.showAddToCartFeedback(menuItem.name, size);
        return true;
    }

    // Remover item do carrinho
    removeItem(cartItemId) {
        this.items = this.items.filter(item => item.id !== cartItemId);
        this.saveCart();
        this.updateCartDisplay();
    }

    // Atualizar quantidade de um item
    updateQuantity(cartItemId, quantity) {
        const item = this.items.find(item => item.id === cartItemId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(cartItemId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartDisplay();
            }
        }
    }

    // Limpar carrinho
    clearCart() {
        this.items = [];
        this.saveCart();
        this.updateCartDisplay();
    }

    // Calcular total do carrinho
    getTotal() {
        return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    // Obter quantidade total de itens
    getTotalItems() {
        return this.items.reduce((total, item) => total + item.quantity, 0);
    }

    // Salvar carrinho no localStorage
    saveCart() {
        localStorage.setItem('laricasCart', JSON.stringify(this.items));
    }

    // Carregar carrinho do localStorage
    loadCart() {
        const saved = localStorage.getItem('laricasCart');
        return saved ? JSON.parse(saved) : [];
    }

    // Atualizar display do carrinho
    updateCartDisplay() {
        const cartCount = document.getElementById('cart-count');
        const totalItems = this.getTotalItems();
        
        if (cartCount) {
            cartCount.textContent = totalItems;
            cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
        }

        // Atualizar modal do carrinho se estiver aberto
        if (document.getElementById('cart-modal').classList.contains('active')) {
            this.renderCartModal();
        }
    }

    // Renderizar modal do carrinho
    renderCartModal() {
        const cartItems = document.getElementById('cart-items');
        const cartTotal = document.getElementById('cart-total');

        if (this.items.length === 0) {
            cartItems.innerHTML = `
                <div class="cart-empty">
                    <i class="fas fa-shopping-cart"></i>
                    <p>Seu carrinho está vazio</p>
                    <p>Adicione alguns itens deliciosos!</p>
                </div>
            `;
            cartTotal.textContent = '0,00';
            return;
        }

        cartItems.innerHTML = this.items.map(item => `
            <div class="cart-item">
                <div class="cart-item-image">
                    <img src="${item.image}" alt="${item.name}" onerror="this.src='assets/images/placeholder.jpg'">
                </div>
                <div class="cart-item-details">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-size">${item.size}</div>
                    <div class="cart-item-price">R$ ${item.price.toFixed(2).replace('.', ',')}</div>
                </div>
                <div class="cart-item-actions">
                    <div class="cart-item-quantity">
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span>${item.quantity}</span>
                        <button class="quantity-btn" onclick="cart.updateQuantity('${item.id}', ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="cart-remove-btn" onclick="cart.removeItem('${item.id}')">
                        <i class="fas fa-trash"></i> Remover
                    </button>
                </div>
            </div>
        `).join('');

        cartTotal.textContent = this.getTotal().toFixed(2).replace('.', ',');
    }

    // Mostrar feedback ao adicionar item
    showAddToCartFeedback(itemName, size) {
        // Criar elemento de feedback
        const feedback = document.createElement('div');
        feedback.className = 'add-to-cart-feedback';
        feedback.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${itemName} (${size}) adicionado ao carrinho!</span>
        `;

        // Adicionar estilos inline
        Object.assign(feedback.style, {
            position: 'fixed',
            top: '100px',
            right: '20px',
            background: 'var(--accent-color)',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '8px',
            boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
            zIndex: '1001',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '14px',
            fontWeight: '500',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        document.body.appendChild(feedback);

        // Animar entrada
        setTimeout(() => {
            feedback.style.transform = 'translateX(0)';
        }, 100);

        // Remover após 3 segundos
        setTimeout(() => {
            feedback.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 3000);
    }

    // Abrir modal do carrinho
    openCartModal() {
        const modal = document.getElementById('cart-modal');
        modal.classList.add('active');
        this.renderCartModal();
        document.body.style.overflow = 'hidden';
    }

    // Fechar modal do carrinho
    closeCartModal() {
        const modal = document.getElementById('cart-modal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }

    // Finalizar pedido
    checkout() {
        if (this.items.length === 0) {
            alert('Seu carrinho está vazio!');
            return;
        }

        // Criar mensagem para WhatsApp
        let message = '*🍕 PEDIDO LARICAS DELIVERY*\n\n';
        message += '*ITENS:*\n';
        
        this.items.forEach(item => {
            message += `• ${item.name} (${item.size}) - Qtd: ${item.quantity}\n`;
            message += `  R$ ${item.price.toFixed(2).replace('.', ',')} cada\n\n`;
        });

        message += `*TOTAL: R$ ${this.getTotal().toFixed(2).replace('.', ',')}*\n\n`;
        message += 'Por favor, confirme meu pedido e informe o tempo de entrega. Obrigado!';

        // Codificar mensagem para URL
        const encodedMessage = encodeURIComponent(message);
        const whatsappUrl = `https://wa.me/5514999999999?text=${encodedMessage}`;

        // Abrir WhatsApp
        window.open(whatsappUrl, '_blank');

        // Fechar modal
        this.closeCartModal();
    }

    // Obter resumo do carrinho para checkout
    getCartSummary() {
        return {
            items: this.items,
            totalItems: this.getTotalItems(),
            total: this.getTotal(),
            formattedTotal: this.getTotal().toFixed(2).replace('.', ',')
        };
    }
}

// Inicializar carrinho
const cart = new ShoppingCart();

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Botão do carrinho
    const cartBtn = document.getElementById('cart-btn');
    if (cartBtn) {
        cartBtn.addEventListener('click', () => cart.openCartModal());
    }

    // Fechar modal do carrinho
    const cartClose = document.getElementById('cart-close');
    if (cartClose) {
        cartClose.addEventListener('click', () => cart.closeCartModal());
    }

    // Botão de checkout
    const checkoutBtn = document.getElementById('checkout-btn');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', () => cart.checkout());
    }

    // Fechar modal clicando fora
    const cartModal = document.getElementById('cart-modal');
    if (cartModal) {
        cartModal.addEventListener('click', function(e) {
            if (e.target === cartModal) {
                cart.closeCartModal();
            }
        });
    }

    // Tecla ESC para fechar modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            cart.closeCartModal();
        }
    });
});

// Função global para adicionar ao carrinho (usada nos botões)
function addToCart(itemId, size = null, quantity = 1) {
    // Se não foi especificado tamanho, usar o primeiro disponível
    if (!size) {
        const item = MenuData.getMenuItemById(itemId);
        if (item && item.sizes.length > 0) {
            size = item.sizes[0].size;
        }
    }
    
    return cart.addItem(itemId, size, quantity);
}

// Exportar para uso global
window.Cart = cart;
window.addToCart = addToCart;
