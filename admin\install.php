<?php
require_once 'config/database.php';

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Se já estiver instalado, redirecionar para login
if (isInstalled() && $step != 'complete') {
    header('Location: login.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // Testar conexão com banco
        try {
            $db = new Database();
            $success = 'Conexão com banco de dados estabelecida com sucesso!';
            $step = 2;
        } catch (Exception $e) {
            $error = 'Erro na conexão: ' . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Instalar banco de dados
        try {
            $db = new Database();
            $db->install();
            $success = 'Banco de dados instalado com sucesso!';
            $step = 'complete';
        } catch (Exception $e) {
            $error = 'Erro na instalação: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalação - Laricas Delivery Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #d32f2f;
            --primary-dark: #b71c1c;
            --secondary-color: #ff6f00;
            --accent-color: #4caf50;
            --text-dark: #212121;
            --text-light: #757575;
            --background-light: #fafafa;
            --border-color: #e0e0e0;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
        }
        
        .install-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .install-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 0.5rem;
        }
        
        .step.active .step-number {
            background: var(--primary-color);
            color: white;
        }
        
        .step.completed .step-number {
            background: var(--accent-color);
            color: white;
        }
        
        .step-text {
            font-size: 0.9rem;
            color: var(--text-light);
        }
        
        .step.active .step-text {
            color: var(--text-dark);
            font-weight: 500;
        }
        
        .requirements-list {
            list-style: none;
            padding: 0;
        }
        
        .requirements-list li {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .requirements-list li:last-child {
            border-bottom: none;
        }
        
        .requirement-icon {
            width: 20px;
            margin-right: 1rem;
        }
        
        .requirement-icon.success {
            color: var(--accent-color);
        }
        
        .requirement-icon.error {
            color: #f44336;
        }
        
        .db-config {
            background: var(--background-light);
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .config-label {
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .config-value {
            font-family: 'Courier New', monospace;
            color: var(--text-light);
        }
        
        .btn-install {
            background: var(--primary-color);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-install:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(211, 47, 47, 0.3);
        }
        
        .success-icon {
            font-size: 4rem;
            color: var(--accent-color);
            margin-bottom: 1rem;
        }
        
        .complete-section {
            text-align: center;
            padding: 2rem 0;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <i class="fas fa-pizza-slice fa-3x mb-3"></i>
            <h1>Laricas Delivery</h1>
            <p>Instalação do Painel Administrativo</p>
        </div>
        
        <div class="install-body">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?= $step >= 1 ? 'active' : '' ?> <?= $step > 1 ? 'completed' : '' ?>">
                    <div class="step-number">1</div>
                    <div class="step-text">Verificação</div>
                </div>
                <div class="step <?= $step >= 2 ? 'active' : '' ?> <?= $step > 2 ? 'completed' : '' ?>">
                    <div class="step-number">2</div>
                    <div class="step-text">Instalação</div>
                </div>
                <div class="step <?= $step == 'complete' ? 'active completed' : '' ?>">
                    <div class="step-number">3</div>
                    <div class="step-text">Concluído</div>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- Step 1: Requirements Check -->
                <h4 class="mb-3">Verificação de Requisitos</h4>
                
                <ul class="requirements-list">
                    <li>
                        <i class="fas fa-check requirement-icon success"></i>
                        <span>PHP 7.4 ou superior</span>
                        <span class="ms-auto text-success"><?= PHP_VERSION ?></span>
                    </li>
                    <li>
                        <i class="fas fa-<?= extension_loaded('pdo') ? 'check' : 'times' ?> requirement-icon <?= extension_loaded('pdo') ? 'success' : 'error' ?>"></i>
                        <span>Extensão PDO</span>
                        <span class="ms-auto <?= extension_loaded('pdo') ? 'text-success' : 'text-danger' ?>">
                            <?= extension_loaded('pdo') ? 'Instalada' : 'Não instalada' ?>
                        </span>
                    </li>
                    <li>
                        <i class="fas fa-<?= extension_loaded('pdo_mysql') ? 'check' : 'times' ?> requirement-icon <?= extension_loaded('pdo_mysql') ? 'success' : 'error' ?>"></i>
                        <span>Extensão PDO MySQL</span>
                        <span class="ms-auto <?= extension_loaded('pdo_mysql') ? 'text-success' : 'text-danger' ?>">
                            <?= extension_loaded('pdo_mysql') ? 'Instalada' : 'Não instalada' ?>
                        </span>
                    </li>
                    <li>
                        <i class="fas fa-<?= is_writable('../uploads') || mkdir('../uploads', 0755, true) ? 'check' : 'times' ?> requirement-icon <?= is_writable('../uploads') ? 'success' : 'error' ?>"></i>
                        <span>Pasta uploads gravável</span>
                        <span class="ms-auto <?= is_writable('../uploads') ? 'text-success' : 'text-danger' ?>">
                            <?= is_writable('../uploads') ? 'OK' : 'Erro' ?>
                        </span>
                    </li>
                </ul>
                
                <div class="db-config">
                    <h6 class="mb-3"><i class="fas fa-database me-2"></i>Configuração do Banco de Dados</h6>
                    <div class="config-item">
                        <span class="config-label">Servidor:</span>
                        <span class="config-value">localhost:3306</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Banco:</span>
                        <span class="config-value">banco0508</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Usuário:</span>
                        <span class="config-value">root</span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Senha:</span>
                        <span class="config-value">Ha31038866##</span>
                    </div>
                </div>
                
                <form method="POST">
                    <button type="submit" class="btn btn-install">
                        <i class="fas fa-play me-2"></i>
                        Testar Conexão e Continuar
                    </button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Installation -->
                <h4 class="mb-3">Instalação do Banco de Dados</h4>
                
                <p class="text-muted mb-4">
                    Agora vamos criar as tabelas e inserir os dados iniciais no banco de dados.
                    Este processo pode levar alguns segundos.
                </p>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>O que será criado:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Tabelas do sistema (produtos, pedidos, usuários, etc.)</li>
                        <li>Dados iniciais (categorias, produtos de exemplo)</li>
                        <li>Usuário administrador padrão</li>
                        <li>Configurações básicas do sistema</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <button type="submit" class="btn btn-install">
                        <i class="fas fa-download me-2"></i>
                        Instalar Banco de Dados
                    </button>
                </form>
                
            <?php elseif ($step == 'complete'): ?>
                <!-- Step 3: Complete -->
                <div class="complete-section">
                    <i class="fas fa-check-circle success-icon"></i>
                    <h4 class="mb-3">Instalação Concluída!</h4>
                    
                    <p class="text-muted mb-4">
                        O sistema foi instalado com sucesso. Você já pode acessar o painel administrativo.
                    </p>
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-key me-2"></i>Credenciais de Acesso</h6>
                        <p class="mb-1"><strong>Usuário:</strong> admin</p>
                        <p class="mb-0"><strong>Senha:</strong> password</p>
                        <small class="text-muted d-block mt-2">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Altere a senha após o primeiro login por segurança
                        </small>
                    </div>
                    
                    <a href="login.php" class="btn btn-install">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Acessar Painel Administrativo
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add loading state to buttons
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const button = this.querySelector('button[type="submit"]');
                const originalText = button.innerHTML;
                
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processando...';
                button.disabled = true;
                
                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 10000);
            });
        });
        
        // Auto-clear alerts
        setTimeout(() => {
            document.querySelectorAll('.alert').forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 300);
            });
        }, 5000);
    </script>
</body>
</html>
