name: maid
version: 2.0.7
description: 'The Mobile Artifical Intelligence Distribution (MAID) project is a research initiative focused on the development and deployment of artificial intelligence (AI) systems on mobile devices. The goal of MAID is to create a frontend for AI that is private, secure, and efficient, allowing users to leverage the power of AI without compromising their personal data or privacy.'
publish_to: 'none'

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any
  shared_preferences: ^2.5.2
  flutter_colorpicker: ^1.1.0
  image: ^4.5.2
  crypto: ^3.0.6
  http: ^1.3.0
  yaml: ^3.1.3
  uuid: ^4.5.1
  package_info_plus: ^8.3.0

  # For loading images and GGUF files
  file_picker: ^9.2.1
  url_launcher: ^6.3.1

  # Supported Ecosystems
  llama_sdk: ^0.0.5
  ollama_dart: ^0.2.3
  openai_dart: ^0.5.2
  mistralai_dart: ^0.0.4
  anthropic_sdk_dart: ^0.2.1

  # For searching local network for ollama
  device_info_plus: ^11.3.0
  permission_handler: ^11.3.1
  network_info_plus: ^6.1.3
  lan_scanner: ^4.0.0+1

  # The maintainer of this package has not updated the package on pub.dev
  # https://github.com/KasemJaffer/receive_sharing_intent/issues/354
  receive_sharing_intent:
    git:
      url: https://github.com/KasemJaffer/receive_sharing_intent
      ref: 2cea396843cd3ab1b5ec4334be4233864637874e
  web: ^1.1.1
  dio: ^5.8.0+1
  path_provider: ^2.1.5
  supabase_flutter: ^2.8.4

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

flutter:
  generate: true
  uses-material-design: true
  assets:
    - images/logo.png
    - images/logo-dark.png
    - huggingface.yaml

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "images/logo.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "images/logo-foreground.png"
  adaptive_icon_monochrome: "images/logo-mono.png"
  windows:
    generate: true
    image_path: "images/logo.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "images/logo.png"
