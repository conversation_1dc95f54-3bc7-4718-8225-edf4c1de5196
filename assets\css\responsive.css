/* Responsive Design */

/* Large Tablets and Small Desktops */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-cta {
        gap: var(--spacing-md);
    }
    
    .highlights-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);
    }
    
    .about-content {
        gap: var(--spacing-2xl);
    }
    
    .contact-content {
        gap: var(--spacing-2xl);
    }
    
    .footer-content {
        gap: var(--spacing-2xl);
    }
}

/* Tablets */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--background-white);
        box-shadow: var(--shadow-medium);
        transform: translateY(-100%);
        transition: transform var(--transition-normal);
        z-index: 999;
    }
    
    .nav-menu.active {
        transform: translateY(0);
    }
    
    .nav-list {
        flex-direction: column;
        padding: var(--spacing-lg);
        gap: var(--spacing-lg);
    }
    
    .menu-toggle {
        display: flex;
    }
    
    .menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Hero Section */
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .hero-cta .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .hero-info {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    /* Sections */
    section {
        padding: var(--spacing-2xl) 0;
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-xl);
    }
    
    /* Highlights */
    .highlights-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    /* Menu */
    .menu-categories {
        gap: var(--spacing-sm);
    }
    
    .category-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .menu-items {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    /* About */
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .about-features {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-lg);
    }
    
    .about-image {
        order: -1;
    }
    
    .about-image img {
        height: 300px;
    }
    
    /* Testimonials */
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    /* Contact */
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .contact-info {
        order: 2;
    }
    
    .contact-form {
        order: 1;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    /* Modal */
    .modal {
        padding: var(--spacing-md);
    }
    
    .modal-content {
        max-height: 90vh;
    }
}

/* Mobile Phones */
@media (max-width: 480px) {
    /* Container */
    .container {
        padding: 0 var(--spacing-md);
    }
    
    /* Typography */
    h1 { font-size: var(--font-size-2xl); }
    h2 { font-size: var(--font-size-xl); }
    h3 { font-size: var(--font-size-lg); }
    
    /* Navigation */
    .nav-brand {
        gap: var(--spacing-sm);
    }
    
    .logo {
        height: 32px;
    }
    
    .brand-text {
        font-size: var(--font-size-lg);
    }
    
    /* Hero */
    .hero-content {
        padding-top: 100px;
    }
    
    .hero-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-xl);
    }
    
    .info-item {
        font-size: var(--font-size-xs);
        text-align: center;
    }
    
    /* Buttons */
    .btn-large {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }
    
    /* Cards */
    .highlight-card,
    .menu-item {
        margin: 0 var(--spacing-sm);
    }
    
    .highlight-image,
    .menu-item-image {
        height: 160px;
    }
    
    .highlight-content,
    .menu-item-content {
        padding: var(--spacing-md);
    }
    
    .highlight-price {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }
    
    /* Menu Items */
    .menu-item-header {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: flex-start;
    }
    
    .menu-item-actions {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .quantity-controls {
        justify-content: center;
    }
    
    /* Contact */
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .contact-item i {
        align-self: center;
    }
    
    /* Cart */
    .cart-item {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .cart-item-actions {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    /* Modal */
    .modal {
        padding: var(--spacing-sm);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
}

/* Extra Small Devices */
@media (max-width: 360px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .hero-cta .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .menu-categories {
        flex-direction: column;
        align-items: center;
    }
    
    .category-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .highlight-card,
    .menu-item {
        margin: 0;
    }
    
    .testimonial-card {
        padding: var(--spacing-md);
    }
}

/* Landscape Orientation for Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        min-height: auto;
        padding: var(--spacing-3xl) 0;
    }
    
    .hero-content {
        padding-top: 80px;
    }
    
    .hero-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .hero-cta {
        margin-bottom: var(--spacing-md);
    }
    
    .hero-info {
        gap: var(--spacing-md);
    }
}

/* Print Styles */
@media print {
    .header,
    .hero,
    .modal,
    .cart-btn,
    .menu-toggle,
    .btn,
    .social-links {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    section {
        padding: 20pt 0;
        page-break-inside: avoid;
    }
    
    .section-title {
        font-size: 18pt;
        margin-bottom: 15pt;
    }
    
    .menu-item,
    .highlight-card,
    .testimonial-card {
        box-shadow: none;
        border: 1pt solid #ccc;
        page-break-inside: avoid;
        margin-bottom: 15pt;
    }
    
    .menu-item-image,
    .highlight-image {
        height: auto;
        max-height: 150pt;
    }
    
    .price {
        font-weight: bold;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo,
    .footer-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .highlight-card:hover,
    .menu-item:hover {
        transform: none;
    }
    
    .highlight-card:hover .highlight-image img {
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-dark: #ffffff;
        --text-light: #b0b0b0;
        --background-white: #1a1a1a;
        --background-light: #2a2a2a;
        --border-color: #404040;
        --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
        --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
        --shadow-heavy: 0 8px 32px rgba(0,0,0,0.5);
    }
    
    .header {
        background-color: var(--background-white);
        border-bottom: 1px solid var(--border-color);
    }
    
    .nav-menu {
        background-color: var(--background-white);
    }
    
    .hero-overlay {
        background: rgba(0, 0, 0, 0.5);
    }
    
    .form-group input,
    .form-group textarea {
        background-color: var(--background-white);
        color: var(--text-dark);
        border-color: var(--border-color);
    }
    
    .form-group input:focus,
    .form-group textarea:focus {
        border-color: var(--primary-color);
    }
}

/* Focus Styles for Accessibility */
.btn:focus,
.nav-link:focus,
.cart-btn:focus,
.menu-toggle:focus,
.category-btn:focus,
.size-option:focus,
.quantity-btn:focus,
input:focus,
textarea:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--text-white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: 1001;
}

.skip-link:focus {
    top: 6px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error States */
.error {
    border-color: #f44336 !important;
    background-color: rgba(244, 67, 54, 0.05);
}

.error-message {
    color: #f44336;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

/* Success States */
.success {
    border-color: var(--accent-color) !important;
    background-color: rgba(76, 175, 80, 0.05);
}

.success-message {
    color: var(--accent-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}
