<?php
/**
 * Configuração do Banco de Dados - Laricas Delivery Admin
 */

class Database {
    private $host = 'localhost';
    private $port = '3306';
    private $db_name = 'banco0508';
    private $username = 'root';
    private $password = 'Ha31038866##';
    private $charset = 'utf8mb4';
    private $pdo;
    
    public function __construct() {
        $this->connect();
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Erro de conexão com o banco de dados");
        }
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Erro na consulta ao banco de dados");
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->pdo->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $set = [];
        foreach ($data as $key => $value) {
            $set[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $set);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    public function commit() {
        return $this->pdo->commit();
    }
    
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    public function rowCount($stmt) {
        return $stmt->rowCount();
    }
    
    // Método para executar scripts SQL (útil para instalação)
    public function executeScript($scriptPath) {
        if (!file_exists($scriptPath)) {
            throw new Exception("Script SQL não encontrado: {$scriptPath}");
        }
        
        $sql = file_get_contents($scriptPath);
        $statements = explode(';', $sql);
        
        $this->beginTransaction();
        
        try {
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $this->pdo->exec($statement);
                }
            }
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    // Verificar se as tabelas existem
    public function tablesExist() {
        try {
            $tables = ['admins', 'categories', 'products', 'orders', 'settings'];
            foreach ($tables as $table) {
                $stmt = $this->pdo->query("SHOW TABLES LIKE '{$table}'");
                if ($stmt->rowCount() == 0) {
                    return false;
                }
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    // Instalar banco de dados
    public function install() {
        try {
            $schemaPath = __DIR__ . '/../database/schema.sql';
            $dataPath = __DIR__ . '/../database/initial_data.sql';
            
            // Executar schema
            $this->executeScript($schemaPath);
            
            // Executar dados iniciais
            $this->executeScript($dataPath);
            
            return true;
        } catch (Exception $e) {
            error_log("Database installation failed: " . $e->getMessage());
            throw new Exception("Erro na instalação do banco de dados: " . $e->getMessage());
        }
    }
}

// Função global para obter conexão com o banco
function getDB() {
    static $db = null;
    if ($db === null) {
        $db = new Database();
    }
    return $db;
}

// Função para verificar se o sistema está instalado
function isInstalled() {
    try {
        $db = getDB();
        return $db->tablesExist();
    } catch (Exception $e) {
        return false;
    }
}
?>
