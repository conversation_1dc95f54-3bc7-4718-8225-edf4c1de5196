{"@@locale": "ko", "friendlyName": "한국어", "localeTitle": "로케일", "defaultLocale": "기본 로케일", "loading": "로딩 중...", "loadModel": "모델 불러오기", "downloadModel": "모델 다운로드", "noModelSelected": "선택된 모델 없음", "noModelLoaded": "로드된 모델 없음", "localModels": "로컬 모델", "size": "크기", "parameters": "매개변수", "delete": "삭제", "select": "선택", "import": "가져오기", "export": "내보내기", "edit": "편집", "regenerate": "재생성", "chatsTitle": "채팅", "newChat": "새 채팅", "anErrorOccurred": "오류가 발생했습니다", "errorTitle": "오류", "key": "키", "value": "값", "ok": "확인", "proceed": "계속 진행", "done": "완료", "close": "닫기", "save": "저장", "saveLabel": "{label} 저장", "selectTag": "태그 선택", "next": "다음", "previous": "이전", "contentShared": "콘텐츠 공유됨", "setUserImage": "사용자 이미지 설정", "setAssistantImage": "어시스턴트 이미지 설정", "loadUserImage": "사용자 이미지 불러오기", "loadAssistantImage": "어시스턴트 이미지 불러오기", "userName": "사용자 이름", "assistantName": "어시스턴트 이름", "user": "사용자", "assistant": "어시스턴트", "cancel": "취소", "aiEcosystem": "AI 생태계", "llamaCpp": "Llama CPP", "llamaCppModel": "Llama CPP 모델", "remoteModel": "원격 모델", "refreshRemoteModels": "원격 모델 새로고침", "ollama": "Ollama", "searchLocalNetwork": "로컬 네트워크 검색", "localNetworkSearchTitle": "로컬 네트워크 검색", "localNetworkSearchContent": "이 기능을 사용하려면 Ollama 인스턴스를 검색하기 위한 추가 권한이 필요합니다.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "모델 매개변수", "addParameter": "매개변수 추가", "removeParameter": "매개변수 제거", "saveParameters": "매개변수 저장", "importParameters": "매개변수 가져오기", "exportParameters": "매개변수 내보내기", "selectAiEcosystem": "AI 생태계 선택", "selectRemoteModel": "원격 모델 선택", "selectThemeMode": "앱 테마 모드 선택", "themeMode": "테마 모드", "themeModeSystem": "시스템", "themeModeLight": "라이트", "themeModeDark": "다크", "themeSeedColor": "테마 기본 색상", "editMessage": "메시지 편집", "settingsTitle": "설정", "aiSettings": "{aiType} 설정", "userSettings": "사용자 설정", "assistantSettings": "어시스턴트 설정", "systemSettings": "시스템 설정", "systemPrompt": "시스템 프롬프트", "clearChats": "채팅 기록 삭제", "resetSettings": "설정 초기화", "clearCache": "캐시 삭제", "aboutTitle": "정보", "aboutContent": "Maid는 로컬에서 llama.cpp 모델과 원격에서 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Google Gemini 및 OpenAI 모델을 사용할 수 있는 크로스플랫폼 무료 오픈 소스 애플리케이션입니다. Maid는 Sillytavern 캐릭터 카드를 지원하여 좋아하는 캐릭터와 상호 작용할 수 있습니다. Maid는 Hugging Face에서 직접 큐레이션된 모델 목록을 앱 내에서 다운로드할 수 있도록 지원합니다. Maid는 MIT 라이선스로 배포되며, 명시적이거나 암시적인 어떠한 보증도 제공되지 않습니다. Maid는 Hugging Face, Meta (Facebook), MistralAI, OpenAI, Google, Microsoft 또는 이 애플리케이션과 호환되는 모델을 제공하는 다른 회사와 제휴하지 않습니다.", "leadMaintainer": "수석 유지 관리자", "apiKey": "API 키", "baseUrl": "기본 URL", "scrollToRecent": "최근 메시지로 스크롤", "clearPrompt": "프롬프트 삭제", "submitPrompt": "프롬프트 제출", "stopPrompt": "프롬프트 중지", "typeMessage": "메시지를 입력하세요...", "code": "코드", "copyLabel": "{label} 복사", "labelCopied": "{label}이(가) 클립보드에 복사되었습니다!", "debugTitle": "디버그", "warning": "경고", "nsfwWarning": "이 모델은 NSFW(부적절한 콘텐츠)를 생성하도록 의도적으로 설계되었습니다. 여기에는 고문, 강간, 살인 및/또는 성적으로 일탈된 행동과 관련된 노골적인 성적 또는 폭력적인 콘텐츠가 포함될 수 있습니다. 이러한 주제에 민감하시거나 해당 주제에 대한 논의가 현지 법률을 위반하는 경우, 진행하지 마십시오.", "login": "로그인", "logout": "로그아웃", "register": "가입하기", "email": "이메일", "password": "비밀번호", "confirmPassword": "비밀번호 확인", "resetCode": "재설정 코드", "resetCodeSent": "재설정 코드가 이메일로 전송되었습니다.", "sendResetCode": "재설정 코드 전송", "sendAgain": "다시 보내기", "required": "필수", "invalidEmail": "유효한 이메일을 입력하세요", "invalidUserName": "3-24자, 영문자, 숫자 또는 밑줄만 사용 가능", "invalidPasswordLength": "최소 8자", "invalidPassword": "대문자, 소문자, 숫자 및 기호를 포함하세요", "passwordNoMatch": "비밀번호가 일치하지 않습니다", "createAccount": "계정 생성", "resetPassword": "비밀번호 재설정", "backToLogin": "로그인으로 돌아가기", "alreadyHaveAccount": "이미 계정이 있습니다", "success": "성공", "registrationSuccess": "등록이 성공적으로 완료되었습니다", "resetSuccess": "비밀번호가 성공적으로 재설정되었습니다.", "emailVerify": "확인을 위해 이메일을 확인하세요."}