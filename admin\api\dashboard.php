<?php
/**
 * API do Dashboard - Laricas Delivery Admin
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar autenticação
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Não autenticado'], 401);
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    $db = getDB();
    
    if ($method === 'GET') {
        $type = $_GET['type'] ?? 'overview';
        
        switch ($type) {
            case 'overview':
                handleOverview($db);
                break;
            case 'sales':
                handleSales($db);
                break;
            case 'orders':
                handleOrders($db);
                break;
            case 'products':
                handleProducts($db);
                break;
            default:
                jsonResponse(['success' => false, 'message' => 'Tipo inválido'], 400);
        }
    } else {
        jsonResponse(['success' => false, 'message' => 'Método não permitido'], 405);
    }
} catch (Exception $e) {
    error_log("Dashboard API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Erro interno do servidor'], 500);
}

function handleOverview($db) {
    $today = date('Y-m-d');
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    $thisMonth = date('Y-m');
    $lastMonth = date('Y-m', strtotime('-1 month'));
    
    // Estatísticas de hoje
    $todayStats = $db->fetch("
        SELECT 
            COUNT(*) as orders_count,
            COALESCE(SUM(total_amount), 0) as total_sales,
            COALESCE(AVG(total_amount), 0) as avg_order_value
        FROM orders 
        WHERE DATE(created_at) = :today AND status != 'cancelled'
    ", ['today' => $today]);
    
    // Estatísticas de ontem
    $yesterdayStats = $db->fetch("
        SELECT 
            COUNT(*) as orders_count,
            COALESCE(SUM(total_amount), 0) as total_sales
        FROM orders 
        WHERE DATE(created_at) = :yesterday AND status != 'cancelled'
    ", ['yesterday' => $yesterday]);
    
    // Estatísticas do mês
    $monthStats = $db->fetch("
        SELECT 
            COUNT(*) as orders_count,
            COALESCE(SUM(total_amount), 0) as total_sales
        FROM orders 
        WHERE DATE_FORMAT(created_at, '%Y-%m') = :month AND status != 'cancelled'
    ", ['month' => $thisMonth]);
    
    // Estatísticas do mês passado
    $lastMonthStats = $db->fetch("
        SELECT 
            COUNT(*) as orders_count,
            COALESCE(SUM(total_amount), 0) as total_sales
        FROM orders 
        WHERE DATE_FORMAT(created_at, '%Y-%m') = :month AND status != 'cancelled'
    ", ['month' => $lastMonth]);
    
    // Pedidos pendentes
    $pendingOrders = $db->fetch("
        SELECT COUNT(*) as count 
        FROM orders 
        WHERE status IN ('pending', 'confirmed', 'preparing')
    ")['count'];
    
    // Produtos ativos
    $activeProducts = $db->fetch("
        SELECT COUNT(*) as count 
        FROM products 
        WHERE status = 'active'
    ")['count'];
    
    // Clientes únicos
    $totalCustomers = $db->fetch("
        SELECT COUNT(DISTINCT customer_id) as count 
        FROM orders
    ")['count'];
    
    // Calcular variações percentuais
    $salesGrowth = $yesterdayStats['total_sales'] > 0 
        ? (($todayStats['total_sales'] - $yesterdayStats['total_sales']) / $yesterdayStats['total_sales']) * 100 
        : 0;
    
    $ordersGrowth = $yesterdayStats['orders_count'] > 0 
        ? (($todayStats['orders_count'] - $yesterdayStats['orders_count']) / $yesterdayStats['orders_count']) * 100 
        : 0;
    
    $monthSalesGrowth = $lastMonthStats['total_sales'] > 0 
        ? (($monthStats['total_sales'] - $lastMonthStats['total_sales']) / $lastMonthStats['total_sales']) * 100 
        : 0;
    
    jsonResponse([
        'success' => true,
        'data' => [
            'today' => [
                'orders' => intval($todayStats['orders_count']),
                'sales' => floatval($todayStats['total_sales']),
                'avg_order_value' => floatval($todayStats['avg_order_value']),
                'sales_growth' => round($salesGrowth, 1),
                'orders_growth' => round($ordersGrowth, 1)
            ],
            'month' => [
                'orders' => intval($monthStats['orders_count']),
                'sales' => floatval($monthStats['total_sales']),
                'growth' => round($monthSalesGrowth, 1)
            ],
            'totals' => [
                'pending_orders' => intval($pendingOrders),
                'active_products' => intval($activeProducts),
                'total_customers' => intval($totalCustomers)
            ]
        ]
    ]);
}

function handleSales($db) {
    $period = $_GET['period'] ?? '7days';
    $startDate = '';
    $endDate = date('Y-m-d');
    
    switch ($period) {
        case '7days':
            $startDate = date('Y-m-d', strtotime('-7 days'));
            break;
        case '30days':
            $startDate = date('Y-m-d', strtotime('-30 days'));
            break;
        case '3months':
            $startDate = date('Y-m-d', strtotime('-3 months'));
            break;
        case '12months':
            $startDate = date('Y-m-d', strtotime('-12 months'));
            break;
        default:
            $startDate = date('Y-m-d', strtotime('-7 days'));
    }
    
    // Vendas por dia
    $salesByDay = $db->fetchAll("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as orders,
            COALESCE(SUM(total_amount), 0) as sales
        FROM orders 
        WHERE DATE(created_at) BETWEEN :start_date AND :end_date 
        AND status != 'cancelled'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ", ['start_date' => $startDate, 'end_date' => $endDate]);
    
    // Vendas por método de pagamento
    $salesByPayment = $db->fetchAll("
        SELECT 
            payment_method,
            COUNT(*) as orders,
            COALESCE(SUM(total_amount), 0) as sales
        FROM orders 
        WHERE DATE(created_at) BETWEEN :start_date AND :end_date 
        AND status != 'cancelled'
        GROUP BY payment_method
        ORDER BY sales DESC
    ", ['start_date' => $startDate, 'end_date' => $endDate]);
    
    // Vendas por categoria
    $salesByCategory = $db->fetchAll("
        SELECT 
            c.name as category,
            COUNT(DISTINCT o.id) as orders,
            COALESCE(SUM(oi.total_price), 0) as sales
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN products p ON oi.product_id = p.id
        JOIN categories c ON p.category_id = c.id
        WHERE DATE(o.created_at) BETWEEN :start_date AND :end_date 
        AND o.status != 'cancelled'
        GROUP BY c.id, c.name
        ORDER BY sales DESC
    ", ['start_date' => $startDate, 'end_date' => $endDate]);
    
    jsonResponse([
        'success' => true,
        'data' => [
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'sales_by_day' => $salesByDay,
            'sales_by_payment' => $salesByPayment,
            'sales_by_category' => $salesByCategory
        ]
    ]);
}

function handleOrders($db) {
    $limit = min(50, intval($_GET['limit'] ?? 10));
    
    // Pedidos recentes
    $recentOrders = $db->fetchAll("
        SELECT 
            id, order_number, customer_name, customer_phone,
            total_amount, status, created_at,
            (SELECT COUNT(*) FROM order_items oi WHERE oi.order_id = orders.id) as items_count
        FROM orders 
        ORDER BY created_at DESC 
        LIMIT :limit
    ", ['limit' => $limit]);
    
    // Status dos pedidos
    $ordersByStatus = $db->fetchAll("
        SELECT 
            status,
            COUNT(*) as count,
            COALESCE(SUM(total_amount), 0) as total_value
        FROM orders 
        WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY status
        ORDER BY count DESC
    ");
    
    // Horários de pico
    $ordersByHour = $db->fetchAll("
        SELECT 
            HOUR(created_at) as hour,
            COUNT(*) as orders
        FROM orders 
        WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        AND status != 'cancelled'
        GROUP BY HOUR(created_at)
        ORDER BY hour ASC
    ");
    
    jsonResponse([
        'success' => true,
        'data' => [
            'recent_orders' => $recentOrders,
            'orders_by_status' => $ordersByStatus,
            'orders_by_hour' => $ordersByHour
        ]
    ]);
}

function handleProducts($db) {
    $limit = min(50, intval($_GET['limit'] ?? 10));
    
    // Produtos mais vendidos
    $topProducts = $db->fetchAll("
        SELECT 
            p.id, p.name, p.image,
            COUNT(oi.id) as orders_count,
            SUM(oi.quantity) as total_quantity,
            COALESCE(SUM(oi.total_price), 0) as total_sales
        FROM products p
        JOIN order_items oi ON p.id = oi.product_id
        JOIN orders o ON oi.order_id = o.id
        WHERE DATE(o.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND o.status != 'cancelled'
        GROUP BY p.id, p.name, p.image
        ORDER BY total_sales DESC
        LIMIT :limit
    ", ['limit' => $limit]);
    
    // Produtos com baixo estoque (simulado)
    $lowStockProducts = $db->fetchAll("
        SELECT 
            id, name, image, status
        FROM products 
        WHERE status = 'out_of_stock'
        ORDER BY updated_at DESC
        LIMIT :limit
    ", ['limit' => $limit]);
    
    // Produtos por categoria
    $productsByCategory = $db->fetchAll("
        SELECT 
            c.name as category,
            COUNT(p.id) as total_products,
            COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_products
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id
        GROUP BY c.id, c.name
        ORDER BY total_products DESC
    ");
    
    jsonResponse([
        'success' => true,
        'data' => [
            'top_products' => $topProducts,
            'low_stock_products' => $lowStockProducts,
            'products_by_category' => $productsByCategory
        ]
    ]);
}
?>
