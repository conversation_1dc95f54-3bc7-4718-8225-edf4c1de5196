AntiFeatures:
  NonFreeNet:
    en-US: Remote models rely on Mistra<PERSON>, Google Gemini and OpenAI.
Categories:
  - Reading
License: MIT
AuthorName: Mobile Artificial Intelligence
AuthorEmail: <EMAIL>
SourceCode: https://github.com/Mobile-Artificial-Intelligence/maid
IssueTracker: https://github.com/Mobile-Artificial-Intelligence/maid/issues

AutoName: maid

RepoType: git
Repo: https://github.com/Mobile-Artificial-Intelligence/maid

Builds:
  - versionName: 1.2.5
    versionCode: 21
    commit: 16bd83bc959067d75e0de750d45f40f719d6b479
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-android-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.5
    versionCode: 22
    commit: 16bd83bc959067d75e0de750d45f40f719d6b479
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-android-arm64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.6
    versionCode: 261
    commit: e8c556328a5975d3068c9b47ce90a4f29b81cc5e
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.6
    versionCode: 262
    commit: e8c556328a5975d3068c9b47ce90a4f29b81cc5e
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.7
    versionCode: 271
    commit: 2d227224555720223814d0308ed96c769f65dacc
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.7
    versionCode: 272
    commit: 2d227224555720223814d0308ed96c769f65dacc
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.8
    versionCode: 281
    commit: e38f87990581c6216e4b415fc0e54aa129f0b6c8
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.8
    versionCode: 282
    commit: e38f87990581c6216e4b415fc0e54aa129f0b6c8
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.9
    versionCode: 16971
    commit: 18f8f7d3941ba99d2f8b17cc95d1eea08e93f1a4
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-android-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 1.2.9
    versionCode: 16972
    commit: 18f8f7d3941ba99d2f8b17cc95d1eea08e93f1a4
    submodules: true
    sudo:
      - mkdir -p /builds/mobile-artificial-intelligence
      - chown vagrant /builds/mobile-artificial-intelligence
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/%v/maid-android-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - export repo=/builds/mobile-artificial-intelligence
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/builds/mobile-artificial-intelligence
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=$(pwd)/.pub-cache
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.1
    versionCode: 20681
    commit: 8f3fc7c03ee1fe4d4f75c03409ff03718c73b217
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.1
    versionCode: 20682
    commit: 8f3fc7c03ee1fe4d4f75c03409ff03718c73b217
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - packages/flutter
      - .pub-cache
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.2
    versionCode: 21221
    commit: 7f20f59d861e72f6c50a9d7fdffacff3c7f84c3e
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.2
    versionCode: 21222
    commit: 7f20f59d861e72f6c50a9d7fdffacff3c7f84c3e
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - packages/flutter
      - .pub-cache
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.3
    versionCode: 21821
    commit: 0658b5eb7c4d252f08e8531b4e55167e5a8c11f3
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.3
    versionCode: 21822
    commit: 0658b5eb7c4d252f08e8531b4e55167e5a8c11f3
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - packages/flutter
      - .pub-cache
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.4
    versionCode: 22531
    commit: 8932a5605ec22ee4483edceb81c56d0a0d642fec
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-x86_64-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-x86_64.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - .pub-cache
      - packages/flutter
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-x64"
      - popd
      - mv $repo/maid com.danemadsen.maid

  - versionName: 2.0.4
    versionCode: 22532
    commit: 8932a5605ec22ee4483edceb81c56d0a0d642fec
    submodules: true
    sudo:
      - mkdir -p /home/<USER>
      - chown vagrant /home/<USER>
    output: build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
    binary: https://github.com/Mobile-Artificial-Intelligence/maid/releases/download/v%v/maid-android-arm64-v8a.apk
    rm:
      - linux
      - macos
      - windows
    prebuild:
      - rm packages/flutter/engine/src/.gn
      - export repo=/home/<USER>/work/maid
      - mkdir -p $repo
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - packages/flutter/bin/flutter config --no-analytics
      - packages/flutter/bin/flutter packages pub get
      - mv $PUB_CACHE .
      - popd
      - mv $repo/maid com.danemadsen.maid
    scanignore:
      - packages/flutter/bin/cache
    scandelete:
      - packages/flutter
      - .pub-cache
    build:
      - export repo=/home/<USER>/work/maid
      - cd ..
      - mv com.danemadsen.maid $repo/maid
      - pushd $repo/maid
      - export PUB_CACHE=/home/<USER>/.pub-cache
      - mv .pub-cache $PUB_CACHE
      - packages/flutter/bin/flutter build apk --release --split-per-abi --target-platform="android-arm64"
      - popd
      - mv $repo/maid com.danemadsen.maid

AllowedAPKSigningKeys: 835ed22ed895c4c272d698aa6e4e48db0b4e36dccf7010d5de15034ac9e1b96f

AutoUpdateMode: Version
UpdateCheckMode: Tags
VercodeOperation:
  - '%c * 10 + 1'
  - '%c * 10 + 2'
UpdateCheckData: pubspec.yaml|version:\s.+\+(\d+)|.|version:\s(.+)\+
CurrentVersion: 2.0.4
CurrentVersionCode: 22532
