// Checkout Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeCheckout();
});

function initializeCheckout() {
    // Check if cart has items
    if (!cart || cart.items.length === 0) {
        showEmptyCart();
        return;
    }

    // Load order summary
    loadOrderSummary();
    
    // Initialize form
    initializeCheckoutForm();
    
    // Initialize payment options
    initializePaymentOptions();
    
    // Initialize address lookup
    initializeAddressLookup();
}

// Show empty cart state
function showEmptyCart() {
    const checkoutContent = document.querySelector('.checkout-content');
    checkoutContent.innerHTML = `
        <div class="empty-cart">
            <i class="fas fa-shopping-cart"></i>
            <h3>Seu carrinho está vazio</h3>
            <p>Adicione alguns itens deliciosos antes de finalizar o pedido!</p>
            <a href="index.html" class="btn btn-primary">
                <i class="fas fa-pizza-slice"></i>
                Ver Cardápio
            </a>
        </div>
    `;
}

// Load order summary
function loadOrderSummary() {
    const checkoutItems = document.getElementById('checkout-items');
    const subtotalEl = document.getElementById('subtotal');
    const deliveryFeeEl = document.getElementById('delivery-fee');
    const finalTotalEl = document.getElementById('final-total');

    if (!checkoutItems) return;

    // Render items
    checkoutItems.innerHTML = cart.items.map(item => `
        <div class="checkout-item">
            <div class="checkout-item-image">
                <img src="${item.image}" alt="${item.name}" onerror="this.src='assets/images/placeholder.jpg'">
            </div>
            <div class="checkout-item-details">
                <div class="checkout-item-name">${item.name}</div>
                <div class="checkout-item-size">${item.size}</div>
                <div class="checkout-item-quantity">Quantidade: ${item.quantity}</div>
            </div>
            <div class="checkout-item-price">
                R$ ${(item.price * item.quantity).toFixed(2).replace('.', ',')}
            </div>
        </div>
    `).join('');

    // Calculate totals
    const subtotal = cart.getTotal();
    const deliveryFee = subtotal >= 30 ? 0 : 5.00;
    const finalTotal = subtotal + deliveryFee;

    // Update totals
    if (subtotalEl) subtotalEl.textContent = `R$ ${subtotal.toFixed(2).replace('.', ',')}`;
    if (deliveryFeeEl) deliveryFeeEl.textContent = deliveryFee === 0 ? 'Grátis' : `R$ ${deliveryFee.toFixed(2).replace('.', ',')}`;
    if (finalTotalEl) finalTotalEl.textContent = `R$ ${finalTotal.toFixed(2).replace('.', ',')}`;
}

// Initialize checkout form
function initializeCheckoutForm() {
    const form = document.getElementById('checkout-form');
    if (!form) return;

    form.addEventListener('submit', handleCheckoutSubmit);

    // Phone mask
    const phoneInput = document.getElementById('customer-phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/^(\d{2})(\d)/g, '($1) $2');
            value = value.replace(/(\d)(\d{4})$/, '$1-$2');
            e.target.value = value;
        });
    }

    // CEP mask
    const cepInput = document.getElementById('cep');
    if (cepInput) {
        cepInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/^(\d{5})(\d)/, '$1-$2');
            e.target.value = value;
        });
    }
}

// Initialize payment options
function initializePaymentOptions() {
    const paymentOptions = document.querySelectorAll('input[name="payment"]');
    const moneyDetails = document.getElementById('money-details');

    paymentOptions.forEach(option => {
        option.addEventListener('change', function() {
            // Hide all payment details
            document.querySelectorAll('.payment-details').forEach(detail => {
                detail.classList.remove('active');
            });

            // Show relevant payment details
            if (this.value === 'money' && moneyDetails) {
                moneyDetails.classList.add('active');
            }
        });
    });

    // Initialize with default selection
    const defaultPayment = document.querySelector('input[name="payment"]:checked');
    if (defaultPayment && defaultPayment.value === 'money' && moneyDetails) {
        moneyDetails.classList.add('active');
    }
}

// Initialize address lookup by CEP
function initializeAddressLookup() {
    const cepInput = document.getElementById('cep');
    if (!cepInput) return;

    cepInput.addEventListener('blur', function() {
        const cep = this.value.replace(/\D/g, '');
        if (cep.length === 8) {
            lookupAddress(cep);
        }
    });
}

// Lookup address by CEP
async function lookupAddress(cep) {
    const streetInput = document.getElementById('street');
    const neighborhoodInput = document.getElementById('neighborhood');
    const cityInput = document.getElementById('city');

    try {
        // Show loading state
        if (streetInput) streetInput.value = 'Carregando...';
        if (neighborhoodInput) neighborhoodInput.value = 'Carregando...';

        const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
        const data = await response.json();

        if (data.erro) {
            throw new Error('CEP não encontrado');
        }

        // Fill address fields
        if (streetInput) streetInput.value = data.logradouro || '';
        if (neighborhoodInput) neighborhoodInput.value = data.bairro || '';
        if (cityInput) cityInput.value = data.localidade || 'Marília';

        // Focus on number field
        const numberInput = document.getElementById('number');
        if (numberInput) numberInput.focus();

    } catch (error) {
        // Clear loading state
        if (streetInput) streetInput.value = '';
        if (neighborhoodInput) neighborhoodInput.value = '';
        
        showMessage('CEP não encontrado. Preencha o endereço manualmente.', 'error');
    }
}

// Handle checkout form submission
function handleCheckoutSubmit(e) {
    e.preventDefault();

    // Validate form
    if (!validateCheckoutForm()) {
        return;
    }

    // Get form data
    const formData = new FormData(e.target);
    const orderData = getOrderData(formData);

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
    submitBtn.disabled = true;

    // Simulate processing delay
    setTimeout(() => {
        // Create WhatsApp message
        const whatsappMessage = createWhatsAppMessage(orderData);
        
        // Open WhatsApp
        const encodedMessage = encodeURIComponent(whatsappMessage);
        const whatsappUrl = `https://wa.me/5514999999999?text=${encodedMessage}`;
        window.open(whatsappUrl, '_blank');

        // Clear cart
        cart.clearCart();

        // Show success message
        showCheckoutSuccess();

        // Reset button (in case user doesn't leave the page)
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// Validate checkout form
function validateCheckoutForm() {
    const errors = [];
    
    // Required fields
    const requiredFields = [
        { id: 'customer-name', name: 'Nome' },
        { id: 'customer-phone', name: 'Telefone' },
        { id: 'cep', name: 'CEP' },
        { id: 'street', name: 'Rua' },
        { id: 'number', name: 'Número' },
        { id: 'neighborhood', name: 'Bairro' },
        { id: 'city', name: 'Cidade' }
    ];

    requiredFields.forEach(field => {
        const input = document.getElementById(field.id);
        if (input) {
            const value = input.value.trim();
            if (!value) {
                errors.push(`${field.name} é obrigatório`);
                input.classList.add('error');
            } else {
                input.classList.remove('error');
            }
        }
    });

    // Validate phone
    const phone = document.getElementById('customer-phone').value;
    if (phone && phone.replace(/\D/g, '').length < 10) {
        errors.push('Telefone deve ter pelo menos 10 dígitos');
        document.getElementById('customer-phone').classList.add('error');
    }

    // Validate email if provided
    const email = document.getElementById('customer-email').value;
    if (email && !isValidEmail(email)) {
        errors.push('E-mail inválido');
        document.getElementById('customer-email').classList.add('error');
    }

    // Validate CEP
    const cep = document.getElementById('cep').value;
    if (cep && cep.replace(/\D/g, '').length !== 8) {
        errors.push('CEP deve ter 8 dígitos');
        document.getElementById('cep').classList.add('error');
    }

    if (errors.length > 0) {
        showMessage(errors.join('\n'), 'error');
        return false;
    }

    return true;
}

// Get order data from form
function getOrderData(formData) {
    const subtotal = cart.getTotal();
    const deliveryFee = subtotal >= 30 ? 0 : 5.00;
    const total = subtotal + deliveryFee;

    return {
        customer: {
            name: formData.get('name'),
            phone: formData.get('phone'),
            email: formData.get('email')
        },
        address: {
            cep: formData.get('cep'),
            street: formData.get('street'),
            number: formData.get('number'),
            complement: formData.get('complement'),
            neighborhood: formData.get('neighborhood'),
            city: formData.get('city'),
            reference: formData.get('reference')
        },
        payment: {
            method: formData.get('payment'),
            changeFor: formData.get('changeFor')
        },
        observations: formData.get('observations'),
        items: cart.items,
        totals: {
            subtotal,
            deliveryFee,
            total
        }
    };
}

// Create WhatsApp message
function createWhatsAppMessage(orderData) {
    let message = '*🍕 PEDIDO LARICAS DELIVERY*\n\n';
    
    // Customer info
    message += '*DADOS DO CLIENTE:*\n';
    message += `Nome: ${orderData.customer.name}\n`;
    message += `Telefone: ${orderData.customer.phone}\n`;
    if (orderData.customer.email) {
        message += `E-mail: ${orderData.customer.email}\n`;
    }
    message += '\n';

    // Address
    message += '*ENDEREÇO DE ENTREGA:*\n';
    message += `${orderData.address.street}, ${orderData.address.number}`;
    if (orderData.address.complement) {
        message += ` - ${orderData.address.complement}`;
    }
    message += `\n${orderData.address.neighborhood} - ${orderData.address.city}\n`;
    message += `CEP: ${orderData.address.cep}\n`;
    if (orderData.address.reference) {
        message += `Referência: ${orderData.address.reference}\n`;
    }
    message += '\n';

    // Items
    message += '*ITENS DO PEDIDO:*\n';
    orderData.items.forEach(item => {
        message += `• ${item.name} (${item.size})\n`;
        message += `  Qtd: ${item.quantity} x R$ ${item.price.toFixed(2).replace('.', ',')}\n`;
        message += `  Subtotal: R$ ${(item.price * item.quantity).toFixed(2).replace('.', ',')}\n\n`;
    });

    // Totals
    message += '*RESUMO DO PEDIDO:*\n';
    message += `Subtotal: R$ ${orderData.totals.subtotal.toFixed(2).replace('.', ',')}\n`;
    message += `Taxa de Entrega: ${orderData.totals.deliveryFee === 0 ? 'Grátis' : `R$ ${orderData.totals.deliveryFee.toFixed(2).replace('.', ',')}`}\n`;
    message += `*TOTAL: R$ ${orderData.totals.total.toFixed(2).replace('.', ',')}*\n\n`;

    // Payment
    message += '*FORMA DE PAGAMENTO:*\n';
    const paymentMethods = {
        'money': 'Dinheiro',
        'card': 'Cartão na Entrega',
        'pix': 'PIX'
    };
    message += `${paymentMethods[orderData.payment.method] || orderData.payment.method}\n`;
    
    if (orderData.payment.method === 'money' && orderData.payment.changeFor) {
        message += `Troco para: R$ ${parseFloat(orderData.payment.changeFor).toFixed(2).replace('.', ',')}\n`;
    }
    message += '\n';

    // Observations
    if (orderData.observations) {
        message += '*OBSERVAÇÕES:*\n';
        message += `${orderData.observations}\n\n`;
    }

    message += 'Por favor, confirme o pedido e informe o tempo de entrega. Obrigado!';

    return message;
}

// Show checkout success
function showCheckoutSuccess() {
    const checkoutContent = document.querySelector('.checkout-content');
    checkoutContent.innerHTML = `
        <div class="checkout-success">
            <i class="fas fa-check-circle"></i>
            <h2>Pedido Enviado com Sucesso!</h2>
            <p>Seu pedido foi enviado via WhatsApp. Em breve entraremos em contato para confirmar e informar o tempo de entrega.</p>
            <div class="success-actions">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Voltar ao Início
                </a>
                <a href="index.html#cardapio" class="btn btn-secondary">
                    <i class="fas fa-pizza-slice"></i>
                    Fazer Novo Pedido
                </a>
            </div>
        </div>
    `;
}

// Utility function for email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Show message function (reused from main.js)
function showMessage(message, type = 'info') {
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message.replace(/\n/g, '<br>')}</span>
        <button class="message-close">&times;</button>
    `;

    // Add styles
    Object.assign(messageEl.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: type === 'success' ? 'var(--accent-color)' : type === 'error' ? '#f44336' : 'var(--primary-color)',
        color: 'white',
        padding: '16px 20px',
        borderRadius: '8px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
        zIndex: '1002',
        display: 'flex',
        alignItems: 'flex-start',
        gap: '12px',
        maxWidth: '400px',
        fontSize: '14px',
        fontWeight: '500',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease'
    });

    // Close button styles
    const closeBtn = messageEl.querySelector('.message-close');
    Object.assign(closeBtn.style, {
        background: 'none',
        border: 'none',
        color: 'white',
        fontSize: '18px',
        cursor: 'pointer',
        padding: '0',
        marginLeft: 'auto',
        flexShrink: '0'
    });

    document.body.appendChild(messageEl);

    // Animate in
    setTimeout(() => {
        messageEl.style.transform = 'translateX(0)';
    }, 100);

    // Close button functionality
    closeBtn.addEventListener('click', () => {
        removeMessage(messageEl);
    });

    // Auto remove after 7 seconds for errors, 5 for others
    const autoRemoveTime = type === 'error' ? 7000 : 5000;
    setTimeout(() => {
        removeMessage(messageEl);
    }, autoRemoveTime);
}

// Remove message
function removeMessage(messageEl) {
    if (messageEl.parentNode) {
        messageEl.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }
}
