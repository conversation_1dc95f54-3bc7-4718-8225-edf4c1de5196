<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

// Verificar se o sistema está instalado
if (!isInstalled()) {
    header('Location: install.php');
    exit;
}

// Se já estiver logado, redirecionar para dashboard
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

// Verificar mensagens da URL
if (isset($_GET['message'])) {
    switch ($_GET['message']) {
        case 'logout_success':
            $success = 'Logout realizado com sucesso!';
            break;
        case 'session_expired':
            $error = 'Sua sessão expirou. Faça login novamente.';
            break;
        case 'access_denied':
            $error = 'Acesso negado. Faça login para continuar.';
            break;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (!empty($username) && !empty($password)) {
        $result = $auth->login($username, $password);

        if ($result['success']) {
            // Verificar se há redirecionamento pendente
            $redirect = $_GET['redirect'] ?? 'dashboard.php';
            header('Location: ' . $redirect);
            exit;
        } else {
            $error = $result['message'];
        }
    } else {
        $error = 'Por favor, preencha todos os campos';
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Laricas Delivery Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #d32f2f;
            --secondary-color: #ff6f00;
            --accent-color: #4caf50;
            --text-dark: #212121;
            --text-light: #757575;
            --background-light: #fafafa;
            --border-color: #e0e0e0;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            overflow: hidden;
            width: 100%;
            max-width: 420px;
            position: relative;
            z-index: 1;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
        }

        .login-header::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: white;
            border-radius: 20px 20px 0 0;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .login-body {
            padding: 2rem 2rem 1rem;
            margin-top: -20px;
            position: relative;
            z-index: 1;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(211, 47, 47, 0.25);
        }

        .form-control.is-valid {
            border-color: var(--accent-color);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%234caf50' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e");
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
        }
        
        .input-group-text {
            background: var(--background-light);
            border: 2px solid var(--border-color);
            border-right: none;
            border-radius: 10px 0 0 10px;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .btn-login {
            background: var(--primary-color);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: #b71c1c;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(211, 47, 47, 0.3);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            font-size: 0.9rem;
        }
        
        .alert-danger {
            background: rgba(244, 67, 54, 0.1);
            color: #d32f2f;
            border: 1px solid rgba(244, 67, 54, 0.2);
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.1);
            color: #2e7d32;
            border: 1px solid rgba(76, 175, 80, 0.2);
        }
        
        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: var(--text-light);
            font-size: 0.9rem;
            border-top: 1px solid var(--border-color);
            margin-top: 1rem;
        }

        .demo-credentials {
            background: var(--background-light);
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            border: 1px solid var(--border-color);
        }

        .demo-credentials h6 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .demo-credentials .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
            font-size: 0.85rem;
        }

        .demo-credentials .credential-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .demo-credentials .credential-value:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                border-radius: 15px;
                max-width: none;
            }

            .login-header {
                padding: 2rem 1.5rem;
            }

            .login-body {
                padding: 1.5rem;
            }

            .login-footer {
                padding: 1rem 1.5rem 1.5rem;
            }

            .demo-credentials {
                margin-top: 0.5rem;
                padding: 0.75rem;
            }
        }

        /* Animações */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .login-header i {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-pizza-slice fa-2x mb-3"></i>
            <h1>Laricas Delivery</h1>
            <p>Painel Administrativo</p>
        </div>
        
        <div class="login-body">
            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>Usuário ou E-mail
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               id="username" 
                               name="username" 
                               placeholder="Digite seu usuário ou e-mail"
                               value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                               required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Senha
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               placeholder="Digite sua senha"
                               required>
                        <button type="button" 
                                class="btn btn-outline-secondary" 
                                id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="remember" 
                               name="remember">
                        <label class="form-check-label" for="remember">
                            Lembrar de mim
                        </label>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login">
                    <span class="login-text">
                        <i class="fas fa-sign-in-alt me-2"></i>Entrar
                    </span>
                    <span class="loading">
                        <i class="fas fa-spinner fa-spin me-2"></i>Entrando...
                    </span>
                </button>
            </form>
        </div>
        
        <div class="login-footer">
            <div class="demo-credentials">
                <h6><i class="fas fa-key me-2"></i>Credenciais de Demonstração</h6>
                <div class="credential-item">
                    <span>Usuário:</span>
                    <span class="credential-value" onclick="fillCredential('username', 'admin')" title="Clique para preencher">admin</span>
                </div>
                <div class="credential-item">
                    <span>Senha:</span>
                    <span class="credential-value" onclick="fillCredential('password', 'password')" title="Clique para preencher">password</span>
                </div>
                <small class="text-muted d-block mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    Clique nas credenciais para preenchimento automático
                </small>
            </div>

            <p class="mt-3 mb-1">&copy; 2024 Laricas Delivery. Todos os direitos reservados.</p>
            <small class="text-muted">Painel Administrativo v1.0</small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');

            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                this.setAttribute('title', 'Ocultar senha');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                this.setAttribute('title', 'Mostrar senha');
            }
        });

        // Fill credential function
        function fillCredential(fieldId, value) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = value;
                field.focus();

                // Add visual feedback
                field.style.backgroundColor = '#e8f5e8';
                setTimeout(() => {
                    field.style.backgroundColor = '';
                }, 1000);
            }
        }

        // Fill both credentials
        function fillAllCredentials() {
            fillCredential('username', 'admin');
            setTimeout(() => {
                fillCredential('password', 'password');
            }, 200);
        }
        
        // Form submission with loading state
        document.getElementById('loginForm').addEventListener('submit', function() {
            const button = this.querySelector('.btn-login');
            const loginText = button.querySelector('.login-text');
            const loading = button.querySelector('.loading');
            
            loginText.style.display = 'none';
            loading.classList.add('show');
            button.disabled = true;
        });
        
        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Clear alert messages after 5 seconds
        function clearAlerts() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.remove();
                        }
                    }, 300);
                }, 5000);
            });
        }

        clearAlerts();

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl + Enter to submit form
            if (e.ctrlKey && e.key === 'Enter') {
                document.getElementById('loginForm').submit();
            }

            // Ctrl + D to fill demo credentials
            if (e.ctrlKey && e.key === 'd') {
                e.preventDefault();
                fillAllCredentials();
            }
        });

        // Add loading animation to pizza icon
        const pizzaIcon = document.querySelector('.login-header i');
        if (pizzaIcon) {
            pizzaIcon.addEventListener('mouseenter', function() {
                this.style.transform = 'rotate(360deg)';
                this.style.transition = 'transform 0.6s ease';
            });

            pizzaIcon.addEventListener('mouseleave', function() {
                this.style.transform = 'rotate(0deg)';
            });
        }

        // Enhanced form validation
        const form = document.getElementById('loginForm');
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');

        function validateField(field, minLength = 1) {
            const value = field.value.trim();
            const isValid = value.length >= minLength;

            if (isValid) {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            } else {
                field.classList.remove('is-valid');
                field.classList.add('is-invalid');
            }

            return isValid;
        }

        usernameField.addEventListener('blur', () => validateField(usernameField));
        passwordField.addEventListener('blur', () => validateField(passwordField));

        // Real-time validation
        usernameField.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.classList.remove('is-invalid');
            }
        });

        passwordField.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.classList.remove('is-invalid');
            }
        });
    </script>
</body>
</html>
