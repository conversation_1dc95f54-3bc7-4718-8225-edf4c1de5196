<?php
/**
 * API de Pedidos - Laricas Delivery Admin
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar autenticação
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Não autenticado'], 401);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        case 'PUT':
            handlePut($db, $input);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Método não permitido'], 405);
    }
} catch (Exception $e) {
    error_log("Orders API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Erro interno do servidor'], 500);
}

function handleGet($db) {
    $id = $_GET['id'] ?? null;
    $status = $_GET['status'] ?? null;
    $date_from = $_GET['date_from'] ?? null;
    $date_to = $_GET['date_to'] ?? null;
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(1, min(100, intval($_GET['limit'] ?? 20)));
    $search = $_GET['search'] ?? '';
    
    if ($id) {
        // Buscar pedido específico
        $sql = "
            SELECT o.*, c.name as customer_name_full, c.email as customer_email_full,
                   (SELECT JSON_ARRAYAGG(
                       JSON_OBJECT('id', oi.id, 'product_id', oi.product_id, 
                                  'product_name', oi.product_name, 'product_size', oi.product_size,
                                  'unit_price', oi.unit_price, 'quantity', oi.quantity, 
                                  'total_price', oi.total_price, 'customizations', oi.customizations,
                                  'notes', oi.notes)
                   ) FROM order_items oi WHERE oi.order_id = o.id) as items
            FROM orders o 
            LEFT JOIN customers c ON o.customer_id = c.id 
            WHERE o.id = :id
        ";
        
        $order = $db->fetch($sql, ['id' => $id]);
        
        if (!$order) {
            jsonResponse(['success' => false, 'message' => 'Pedido não encontrado'], 404);
        }
        
        $order['items'] = json_decode($order['items'] ?? '[]', true) ?: [];
        $order['delivery_address'] = json_decode($order['delivery_address'] ?? '{}', true) ?: [];
        $order['payment_details'] = json_decode($order['payment_details'] ?? '{}', true) ?: [];
        
        jsonResponse(['success' => true, 'data' => $order]);
    } else {
        // Listar pedidos
        $offset = ($page - 1) * $limit;
        $where = ['1=1'];
        $params = [];
        
        if ($status) {
            $where[] = 'o.status = :status';
            $params['status'] = $status;
        }
        
        if ($date_from) {
            $where[] = 'DATE(o.created_at) >= :date_from';
            $params['date_from'] = $date_from;
        }
        
        if ($date_to) {
            $where[] = 'DATE(o.created_at) <= :date_to';
            $params['date_to'] = $date_to;
        }
        
        if ($search) {
            $where[] = '(o.order_number LIKE :search OR o.customer_name LIKE :search OR o.customer_phone LIKE :search)';
            $params['search'] = "%{$search}%";
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Contar total
        $countSql = "SELECT COUNT(*) as total FROM orders o WHERE {$whereClause}";
        $total = $db->fetch($countSql, $params)['total'];
        
        // Buscar pedidos
        $sql = "
            SELECT o.*, 
                   (SELECT COUNT(*) FROM order_items oi WHERE oi.order_id = o.id) as items_count
            FROM orders o 
            WHERE {$whereClause}
            ORDER BY o.created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $orders = $db->fetchAll($sql, $params);
        
        // Decodificar JSON fields
        foreach ($orders as &$order) {
            $order['delivery_address'] = json_decode($order['delivery_address'] ?? '{}', true) ?: [];
            $order['payment_details'] = json_decode($order['payment_details'] ?? '{}', true) ?: [];
        }
        
        $pagination = paginate($total, $limit, $page);
        
        jsonResponse([
            'success' => true,
            'data' => $orders,
            'pagination' => $pagination
        ]);
    }
}

function handlePost($db, $input) {
    if (!hasPermission('edit_orders')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    // Validar dados obrigatórios
    $required = ['customer_name', 'customer_phone', 'delivery_address', 'payment_method', 'items'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            jsonResponse(['success' => false, 'message' => "Campo {$field} é obrigatório"], 400);
        }
    }
    
    if (empty($input['items']) || !is_array($input['items'])) {
        jsonResponse(['success' => false, 'message' => 'Pedido deve ter pelo menos um item'], 400);
    }
    
    $db->beginTransaction();
    
    try {
        // Verificar/criar cliente
        $customer = $db->fetch("SELECT * FROM customers WHERE phone = :phone", 
                              ['phone' => $input['customer_phone']]);
        
        if (!$customer) {
            $customerData = [
                'name' => sanitize($input['customer_name']),
                'phone' => sanitize($input['customer_phone']),
                'email' => sanitize($input['customer_email'] ?? ''),
                'total_orders' => 1,
                'total_spent' => 0
            ];
            $customerId = $db->insert('customers', $customerData);
        } else {
            $customerId = $customer['id'];
        }
        
        // Calcular totais
        $subtotal = 0;
        $validItems = [];
        
        foreach ($input['items'] as $item) {
            if (empty($item['product_id']) || empty($item['quantity']) || empty($item['unit_price'])) {
                continue;
            }
            
            $quantity = intval($item['quantity']);
            $unitPrice = floatval($item['unit_price']);
            $totalPrice = $quantity * $unitPrice;
            
            $validItems[] = [
                'product_id' => intval($item['product_id']),
                'product_name' => sanitize($item['product_name'] ?? ''),
                'product_size' => sanitize($item['product_size'] ?? ''),
                'unit_price' => $unitPrice,
                'quantity' => $quantity,
                'total_price' => $totalPrice,
                'customizations' => !empty($item['customizations']) ? json_encode($item['customizations']) : null,
                'notes' => sanitize($item['notes'] ?? '')
            ];
            
            $subtotal += $totalPrice;
        }
        
        if (empty($validItems)) {
            jsonResponse(['success' => false, 'message' => 'Nenhum item válido encontrado'], 400);
        }
        
        $deliveryFee = floatval($input['delivery_fee'] ?? 0);
        $discountAmount = floatval($input['discount_amount'] ?? 0);
        $totalAmount = $subtotal + $deliveryFee - $discountAmount;
        
        // Criar pedido
        $orderData = [
            'order_number' => generateOrderNumber(),
            'customer_id' => $customerId,
            'customer_name' => sanitize($input['customer_name']),
            'customer_phone' => sanitize($input['customer_phone']),
            'customer_email' => sanitize($input['customer_email'] ?? ''),
            'delivery_address' => json_encode($input['delivery_address']),
            'payment_method' => sanitize($input['payment_method']),
            'payment_details' => !empty($input['payment_details']) ? json_encode($input['payment_details']) : null,
            'subtotal' => $subtotal,
            'delivery_fee' => $deliveryFee,
            'discount_amount' => $discountAmount,
            'total_amount' => $totalAmount,
            'status' => $input['status'] ?? 'pending',
            'notes' => sanitize($input['notes'] ?? ''),
            'estimated_delivery_time' => !empty($input['estimated_delivery_time']) ? $input['estimated_delivery_time'] : null
        ];
        
        $orderId = $db->insert('orders', $orderData);
        
        // Inserir itens do pedido
        foreach ($validItems as $item) {
            $item['order_id'] = $orderId;
            $db->insert('order_items', $item);
        }
        
        // Atualizar estatísticas do cliente
        if ($customer) {
            $db->update('customers', 
                ['total_orders' => $customer['total_orders'] + 1,
                 'total_spent' => $customer['total_spent'] + $totalAmount], 
                'id = :id', 
                ['id' => $customerId]
            );
        }
        
        $db->commit();
        
        logActivity('order_created', 'orders', $orderId, null, $orderData);
        
        // Enviar notificações (simulado)
        sendEmailNotification(
            getSetting('order_notification_email', '<EMAIL>'),
            "Novo Pedido #{$orderData['order_number']}",
            "Novo pedido recebido no valor de " . formatPrice($totalAmount)
        );
        
        jsonResponse([
            'success' => true,
            'message' => 'Pedido criado com sucesso',
            'data' => [
                'id' => $orderId,
                'order_number' => $orderData['order_number']
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

function handlePut($db, $input) {
    if (!hasPermission('edit_orders')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID do pedido é obrigatório'], 400);
    }
    
    // Verificar se pedido existe
    $order = $db->fetch("SELECT * FROM orders WHERE id = :id", ['id' => $id]);
    if (!$order) {
        jsonResponse(['success' => false, 'message' => 'Pedido não encontrado'], 404);
    }
    
    $updateData = [];
    $allowedFields = ['status', 'notes', 'estimated_delivery_time'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $updateData[$field] = sanitize($input[$field]);
        }
    }
    
    // Campos especiais para mudança de status
    if (isset($input['status'])) {
        $newStatus = $input['status'];
        $oldStatus = $order['status'];
        
        if ($newStatus === 'delivered' && $oldStatus !== 'delivered') {
            $updateData['delivered_at'] = date('Y-m-d H:i:s');
        } elseif ($newStatus === 'cancelled' && $oldStatus !== 'cancelled') {
            $updateData['cancelled_at'] = date('Y-m-d H:i:s');
            $updateData['cancellation_reason'] = sanitize($input['cancellation_reason'] ?? '');
        }
    }
    
    if (!empty($updateData)) {
        $db->update('orders', $updateData, 'id = :id', ['id' => $id]);
        
        logActivity('order_updated', 'orders', $id, $order, $updateData);
        
        // Enviar notificação de mudança de status (simulado)
        if (isset($input['status']) && $input['status'] !== $order['status']) {
            $statusText = getOrderStatusText($input['status']);
            sendWhatsAppNotification(
                $order['customer_phone'],
                "Seu pedido #{$order['order_number']} foi atualizado para: {$statusText}"
            );
        }
    }
    
    jsonResponse([
        'success' => true,
        'message' => 'Pedido atualizado com sucesso'
    ]);
}

function handleDelete($db) {
    if (!hasPermission('edit_orders')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID do pedido é obrigatório'], 400);
    }
    
    // Verificar se pedido existe
    $order = $db->fetch("SELECT * FROM orders WHERE id = :id", ['id' => $id]);
    if (!$order) {
        jsonResponse(['success' => false, 'message' => 'Pedido não encontrado'], 404);
    }
    
    // Só permitir deletar pedidos cancelados ou muito antigos
    if ($order['status'] !== 'cancelled') {
        jsonResponse(['success' => false, 'message' => 'Só é possível deletar pedidos cancelados'], 400);
    }
    
    $db->beginTransaction();
    
    try {
        // Deletar itens do pedido
        $db->delete('order_items', 'order_id = :id', ['id' => $id]);
        
        // Deletar pedido
        $db->delete('orders', 'id = :id', ['id' => $id]);
        
        $db->commit();
        
        logActivity('order_deleted', 'orders', $id, $order, null);
        
        jsonResponse([
            'success' => true,
            'message' => 'Pedido deletado com sucesso'
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}
?>
