<?php
/**
 * API de Categorias - Laricas Delivery Admin
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar autenticação
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Não autenticado'], 401);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        case 'PUT':
            handlePut($db, $input);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Método não permitido'], 405);
    }
} catch (Exception $e) {
    error_log("Categories API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Erro interno do servidor'], 500);
}

function handleGet($db) {
    $id = $_GET['id'] ?? null;
    $status = $_GET['status'] ?? 'active';
    
    if ($id) {
        // Buscar categoria específica
        $sql = "
            SELECT c.*,
                   (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.status = 'active') as products_count
            FROM categories c 
            WHERE c.id = :id
        ";
        
        $category = $db->fetch($sql, ['id' => $id]);
        
        if (!$category) {
            jsonResponse(['success' => false, 'message' => 'Categoria não encontrada'], 404);
        }
        
        jsonResponse(['success' => true, 'data' => $category]);
    } else {
        // Listar categorias
        $where = ['1=1'];
        $params = [];
        
        if ($status !== 'all') {
            $where[] = 'c.status = :status';
            $params['status'] = $status;
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "
            SELECT c.*,
                   (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.status = 'active') as products_count
            FROM categories c 
            WHERE {$whereClause}
            ORDER BY c.sort_order ASC, c.name ASC
        ";
        
        $categories = $db->fetchAll($sql, $params);
        
        jsonResponse([
            'success' => true,
            'data' => $categories
        ]);
    }
}

function handlePost($db, $input) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    // Validar dados obrigatórios
    if (empty($input['name'])) {
        jsonResponse(['success' => false, 'message' => 'Nome da categoria é obrigatório'], 400);
    }
    
    // Gerar slug único
    $slug = generateSlug($input['name']);
    $originalSlug = $slug;
    $counter = 1;
    
    while ($db->fetch("SELECT id FROM categories WHERE slug = :slug", ['slug' => $slug])) {
        $slug = $originalSlug . '-' . $counter++;
    }
    
    $categoryData = [
        'name' => sanitize($input['name']),
        'slug' => $slug,
        'description' => sanitize($input['description'] ?? ''),
        'image' => sanitize($input['image'] ?? ''),
        'icon' => sanitize($input['icon'] ?? ''),
        'sort_order' => intval($input['sort_order'] ?? 0),
        'status' => $input['status'] ?? 'active'
    ];
    
    $categoryId = $db->insert('categories', $categoryData);
    
    logActivity('category_created', 'categories', $categoryId, null, $categoryData);
    
    jsonResponse([
        'success' => true,
        'message' => 'Categoria criada com sucesso',
        'data' => ['id' => $categoryId]
    ]);
}

function handlePut($db, $input) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID da categoria é obrigatório'], 400);
    }
    
    // Verificar se categoria existe
    $category = $db->fetch("SELECT * FROM categories WHERE id = :id", ['id' => $id]);
    if (!$category) {
        jsonResponse(['success' => false, 'message' => 'Categoria não encontrada'], 404);
    }
    
    $updateData = [];
    $allowedFields = ['name', 'description', 'image', 'icon', 'sort_order', 'status'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            if ($field === 'sort_order') {
                $updateData[$field] = intval($input[$field]);
            } else {
                $updateData[$field] = sanitize($input[$field]);
            }
        }
    }
    
    // Atualizar slug se nome mudou
    if (isset($input['name']) && $input['name'] !== $category['name']) {
        $slug = generateSlug($input['name']);
        $originalSlug = $slug;
        $counter = 1;
        
        while ($db->fetch("SELECT id FROM categories WHERE slug = :slug AND id != :id", 
                         ['slug' => $slug, 'id' => $id])) {
            $slug = $originalSlug . '-' . $counter++;
        }
        
        $updateData['slug'] = $slug;
    }
    
    if (!empty($updateData)) {
        $db->update('categories', $updateData, 'id = :id', ['id' => $id]);
        
        logActivity('category_updated', 'categories', $id, $category, $updateData);
    }
    
    jsonResponse([
        'success' => true,
        'message' => 'Categoria atualizada com sucesso'
    ]);
}

function handleDelete($db) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID da categoria é obrigatório'], 400);
    }
    
    // Verificar se categoria existe
    $category = $db->fetch("SELECT * FROM categories WHERE id = :id", ['id' => $id]);
    if (!$category) {
        jsonResponse(['success' => false, 'message' => 'Categoria não encontrada'], 404);
    }
    
    // Verificar se categoria tem produtos
    $productCount = $db->fetch("SELECT COUNT(*) as count FROM products WHERE category_id = :id", 
                              ['id' => $id])['count'];
    
    if ($productCount > 0) {
        jsonResponse(['success' => false, 'message' => 'Não é possível deletar categoria com produtos'], 400);
    }
    
    $db->delete('categories', 'id = :id', ['id' => $id]);
    
    logActivity('category_deleted', 'categories', $id, $category, null);
    
    jsonResponse([
        'success' => true,
        'message' => 'Categoria deletada com sucesso'
    ]);
}
?>
