name: Build MacOS

on:
  workflow_call:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/build-macos.yml'
      - 'macos/**'
      - 'lib/**'
      - '*.yaml'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-13, macos-14]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.0'

      - name: Install Apple Certificate
        uses: apple-actions/import-codesign-certs@v1
        with:
          p12-file-base64: ${{ secrets.APPLE_CERTIFICATE_P12 }}
          p12-password: ${{ secrets.APPLE_PASSWORD }}

      - name: Install the provisioning profile
        run: |
          mkdir -p ~/Library/Developer/Xcode/Provisioning\ Profiles
          echo "${{ secrets.APPLE_MACOS_PROVISION_PROFILE }}" | base64 --decode > ~/Library/Developer/Xcode/Provisioning\ Profiles/Github_Actions.provisionprofile

      - name: Build macOS App
        run: |
          flutter pub get
          flutter build macos -v

      - name: Sign macOS App
        run: |
          codesign --deep --force --verbose --options runtime --timestamp --sign "${{ secrets.APPLE_DEVELOPER_ID_APPLICATION }}" build/macos/Build/Products/Release/maid.app

          find build/macos/Build/Products/Release/maid.app -type d -name "*.framework" -exec codesign --force --verbose --options runtime --timestamp --sign "${{ secrets.APPLE_DEVELOPER_ID_APPLICATION }}" {} \;
          find build/macos/Build/Products/Release/maid.app -type f -exec codesign --force --verbose --options runtime --timestamp --sign "${{ secrets.APPLE_DEVELOPER_ID_APPLICATION }}" {} \;

          codesign --verify --deep --strict --verbose build/macos/Build/Products/Release/maid.app

      - name: Compress macOS App
        run: |
          ditto -c -k --sequesterRsrc --keepParent "build/macos/Build/Products/Release/maid.app" "maid.zip" 

      - name: Notarize macOS App
        run: |
          xcrun notarytool store-credentials --apple-id ${{ secrets.APPLE_ID }} --password ${{ secrets.APPLE_APPLICATION_SPECIFIC_PASSWORD }} --team-id ${{ secrets.APPLE_TEAM_ID }} --validate notorization_profile
          xcrun notarytool submit --keychain-profile "notorization_profile" --progress --wait maid.zip

      - name: Staple Notarization Ticket
        run: |
          xcrun stapler staple "build/macos/Build/Products/Release/maid.app"

      - name: Create DMG
        run: ./scripts/create-dmg.sh ${{ matrix.os == 'macos-13' && 'x86_64' || 'arm64' }}

      - name: Upload macOS Build
        uses: actions/upload-artifact@v4
        with:
          name: maid-macos-${{ matrix.os == 'macos-13' && 'x86_64' || 'arm64' }}
          path: dmg
