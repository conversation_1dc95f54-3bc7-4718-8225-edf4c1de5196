// Configuration File - Laricas Delivery
// Centralize all configurable settings here

const CONFIG = {
    // Business Information
    business: {
        name: 'Laricas Delivery',
        tagline: 'A Melhor Pizza da Região de Marília',
        description: 'Sabor incomparável, ingredientes frescos e delivery rápido direto na sua casa!',
        
        // Contact Information
        phone: '5514999999999', // WhatsApp format (country + area + number)
        phoneDisplay: '(14) 99999-9999', // Display format
        email: '<EMAIL>',
        
        // Address (if applicable)
        address: {
            street: '',
            number: '',
            neighborhood: '',
            city: 'Marília',
            state: 'SP',
            zipCode: ''
        },
        
        // Operating Hours
        hours: {
            weekdays: 'Seg-Qui: 18h às 23h',
            weekend: 'Sex-Sáb: 18h à 00h',
            sunday: 'Domingo: 18h às 23h',
            display: 'Seg-Qui: 18h-23h | Sex-Sáb: 18h-00h | Dom: 18h-23h'
        },
        
        // Social Media
        social: {
            facebook: '#',
            instagram: '#',
            whatsapp: 'https://wa.me/5514999999999'
        }
    },

    // Delivery Settings
    delivery: {
        freeDeliveryMinimum: 30.00, // Minimum order for free delivery
        deliveryFee: 5.00, // Standard delivery fee
        deliveryAreas: [
            'Centro',
            'Vila Barbosa',
            'Jardim Alvorada',
            'Alto Cafezal',
            'Nova Marília'
        ]
    },

    // Payment Methods
    payment: {
        methods: [
            { id: 'money', name: 'Dinheiro', icon: 'fas fa-money-bill-wave' },
            { id: 'card', name: 'Cartão na Entrega', icon: 'fas fa-credit-card' },
            { id: 'pix', name: 'PIX', icon: 'fas fa-qrcode' }
        ],
        pixKey: '<EMAIL>' // PIX key if applicable
    },

    // Site Settings
    site: {
        title: 'Laricas Delivery - A Melhor Pizza da Região de Marília',
        description: 'Peça já sua pizza favorita! Delivery rápido e sabor incomparável. A melhor pizza da região de Marília.',
        keywords: 'pizza, delivery, Marília, esfiha, lanches, comida, pedido online',
        url: 'https://laricasdelivery.com.br',
        logo: 'assets/images/logo-laricas.png',
        favicon: 'assets/images/favicon.ico'
    },

    // Theme Colors (CSS Custom Properties)
    theme: {
        primary: '#d32f2f',      // Red
        primaryDark: '#b71c1c',  // Dark Red
        secondary: '#ff6f00',    // Orange
        accent: '#4caf50',       // Green
        textDark: '#212121',     // Dark Gray
        textLight: '#757575',    // Light Gray
        textWhite: '#ffffff',    // White
        backgroundLight: '#fafafa', // Light Background
        backgroundWhite: '#ffffff', // White Background
        borderColor: '#e0e0e0'   // Border Gray
    },

    // API Settings
    api: {
        viaCep: 'https://viacep.com.br/ws/', // CEP lookup API
        timeout: 5000 // API timeout in milliseconds
    },

    // Cart Settings
    cart: {
        maxQuantityPerItem: 10,
        storageKey: 'laricasCart',
        autoSaveInterval: 1000 // Auto-save interval in milliseconds
    },

    // UI Settings
    ui: {
        animationDuration: 300, // Default animation duration in milliseconds
        toastDuration: 5000,    // Toast message duration in milliseconds
        loadingDelay: 300,      // Simulated loading delay for better UX
        
        // Breakpoints for responsive design
        breakpoints: {
            mobile: 480,
            tablet: 768,
            desktop: 1024,
            largeDesktop: 1200
        }
    },

    // SEO and Analytics
    seo: {
        googleAnalyticsId: '', // GA4 Measurement ID
        facebookPixelId: '',   // Facebook Pixel ID
        googleTagManagerId: '', // GTM Container ID
        
        // Schema.org structured data
        schema: {
            '@context': 'http://schema.org',
            '@type': 'Restaurant',
            'name': 'Laricas Delivery',
            'description': 'A melhor pizza da região de Marília com delivery rápido',
            'telephone': '(14) 99999-9999',
            'servesCuisine': 'Pizza, Lanches, Esfihas',
            'priceRange': '$$',
            'openingHours': [
                'Mo-Th 18:00-23:00',
                'Fr-Sa 18:00-00:00',
                'Su 18:00-23:00'
            ]
        }
    },

    // Feature Flags
    features: {
        enableSearch: false,        // Enable menu search functionality
        enableReviews: false,       // Enable customer reviews
        enableLoyalty: false,       // Enable loyalty program
        enableScheduling: false,    // Enable order scheduling
        enableTracking: false,      // Enable order tracking
        enableMultipleAddresses: false, // Enable multiple delivery addresses
        enableCoupons: false,       // Enable discount coupons
        enableWishlist: false       // Enable product wishlist
    },

    // Messages and Texts
    messages: {
        // Success messages
        addedToCart: 'Item adicionado ao carrinho!',
        orderSent: 'Pedido enviado com sucesso!',
        messageSent: 'Mensagem enviada com sucesso!',
        
        // Error messages
        emptyCart: 'Seu carrinho está vazio!',
        invalidCep: 'CEP não encontrado. Preencha o endereço manualmente.',
        requiredField: 'Este campo é obrigatório',
        invalidEmail: 'E-mail inválido',
        invalidPhone: 'Telefone deve ter pelo menos 10 dígitos',
        
        // Loading messages
        loading: 'Carregando...',
        processing: 'Processando...',
        sending: 'Enviando...',
        
        // WhatsApp messages
        whatsappGreeting: 'Olá! Gostaria de fazer um pedido na Laricas Delivery.',
        whatsappOrderHeader: '*🍕 PEDIDO LARICAS DELIVERY*',
        whatsappContactHeader: '*CONTATO SITE LARICAS DELIVERY*'
    },

    // Development Settings
    development: {
        debug: false,           // Enable debug mode
        mockApi: false,         // Use mock API responses
        showPlaceholders: true, // Show placeholder images
        logLevel: 'warn'        // Console log level: 'error', 'warn', 'info', 'debug'
    }
};

// Utility functions for configuration
const ConfigUtils = {
    // Get formatted phone number for WhatsApp
    getWhatsAppUrl: (message = '') => {
        const encodedMessage = encodeURIComponent(message || CONFIG.messages.whatsappGreeting);
        return `https://wa.me/${CONFIG.business.phone}?text=${encodedMessage}`;
    },

    // Get formatted business hours
    getFormattedHours: () => {
        return CONFIG.business.hours.display;
    },

    // Check if delivery is free
    isFreeDelivery: (orderTotal) => {
        return orderTotal >= CONFIG.delivery.freeDeliveryMinimum;
    },

    // Calculate delivery fee
    getDeliveryFee: (orderTotal) => {
        return ConfigUtils.isFreeDelivery(orderTotal) ? 0 : CONFIG.delivery.deliveryFee;
    },

    // Get theme color
    getThemeColor: (colorName) => {
        return CONFIG.theme[colorName] || '#000000';
    },

    // Check if feature is enabled
    isFeatureEnabled: (featureName) => {
        return CONFIG.features[featureName] || false;
    },

    // Get message by key
    getMessage: (messageKey) => {
        return CONFIG.messages[messageKey] || messageKey;
    },

    // Get API endpoint
    getApiEndpoint: (endpoint) => {
        switch (endpoint) {
            case 'cep':
                return CONFIG.api.viaCep;
            default:
                return '';
        }
    },

    // Validate configuration
    validate: () => {
        const errors = [];
        
        if (!CONFIG.business.phone) {
            errors.push('Business phone number is required');
        }
        
        if (!CONFIG.business.name) {
            errors.push('Business name is required');
        }
        
        if (CONFIG.development.debug && errors.length > 0) {
            console.warn('Configuration validation errors:', errors);
        }
        
        return errors.length === 0;
    }
};

// Initialize configuration
document.addEventListener('DOMContentLoaded', function() {
    // Validate configuration
    ConfigUtils.validate();
    
    // Apply theme colors to CSS custom properties
    if (document.documentElement) {
        const root = document.documentElement;
        Object.keys(CONFIG.theme).forEach(key => {
            const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
            root.style.setProperty(cssVar, CONFIG.theme[key]);
        });
    }
    
    // Update page title and meta tags
    if (CONFIG.site.title) {
        document.title = CONFIG.site.title;
    }
    
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription && CONFIG.site.description) {
        metaDescription.setAttribute('content', CONFIG.site.description);
    }
    
    // Log configuration in debug mode
    if (CONFIG.development.debug) {
        console.log('Laricas Delivery Configuration:', CONFIG);
    }
});

// Export for global use
window.CONFIG = CONFIG;
window.ConfigUtils = ConfigUtils;
