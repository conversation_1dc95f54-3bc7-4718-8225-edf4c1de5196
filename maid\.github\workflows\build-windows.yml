name: Build Windows

on:
  workflow_call:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/build-windows.yml'
      - 'windows/**'
      - 'lib/**'
      - '*.yaml'

jobs:        
  build:
    runs-on: windows-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.0'
        
      - name: Install dependencies
        run: |
          choco install -y cmake ninja

      - name: Build Windows App
        run: |
          flutter pub get
          flutter build windows -v

      - name: Upload Windows Build
        uses: actions/upload-artifact@v4
        with:
          name: maid-windows
          path: build/windows/x64/bundle