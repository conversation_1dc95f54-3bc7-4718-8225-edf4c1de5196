{"@@locale": "fr", "friendlyName": "Français", "localeTitle": "<PERSON><PERSON>", "defaultLocale": "Langue par défaut", "loading": "Chargement...", "loadModel": "Charger le modèle", "downloadModel": "Télécharger le modèle", "noModelSelected": "Aucun modèle s<PERSON>", "noModelLoaded": "<PERSON><PERSON><PERSON> mod<PERSON><PERSON> chargé", "localModels": "<PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "parameters": "Paramètres", "delete": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "import": "Importer", "export": "Exporter", "edit": "Modifier", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatsTitle": "Discussions", "newChat": "Nouvelle discussion", "anErrorOccurred": "Une erreur est survenue", "errorTitle": "<PERSON><PERSON><PERSON>", "key": "Clé", "value": "<PERSON><PERSON>", "ok": "OK", "proceed": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "saveLabel": "Enregistrer {label}", "selectTag": "Choisir une étiquette", "next": "Suivant", "previous": "Précédent", "contentShared": "Contenu partagé", "setUserImage": "Définir l'image de l'utilisateur", "setAssistantImage": "Définir l'image de l'assistant", "loadUserImage": "Charger l'image de l'utilisateur", "loadAssistantImage": "Charger l'image de l'assistant", "userName": "Nom d'utilisateur", "assistantName": "Nom de l'assistant", "user": "Utilisa<PERSON>ur", "assistant": "Assistant", "cancel": "Annuler", "aiEcosystem": "Écosystème IA", "llamaCpp": "Llama CPP", "llamaCppModel": "<PERSON><PERSON><PERSON><PERSON> CPP", "remoteModel": "<PERSON><PERSON><PERSON><PERSON> distant", "refreshRemoteModels": "<PERSON><PERSON><PERSON><PERSON><PERSON> les modèles distants", "ollama": "Ollama", "searchLocalNetwork": "Rechercher sur le réseau local", "localNetworkSearchTitle": "Recherche sur le réseau local", "localNetworkSearchContent": "Cette fonctionnalité nécessite des permissions supplémentaires pour rechercher des instances Ollama sur votre réseau local.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Paramètres du modèle", "addParameter": "Ajouter un paramètre", "removeParameter": "Supp<PERSON><PERSON> le paramètre", "saveParameters": "Enregistrer les paramètres", "importParameters": "Importer les paramètres", "exportParameters": "Exporter les paramètres", "selectAiEcosystem": "Sélectionner l'écosystème IA", "selectRemoteModel": "Sélectionner un modèle distant", "selectThemeMode": "Sélectionner le mode du thème", "themeMode": "Mode du thème", "themeModeSystem": "Système", "themeModeLight": "<PERSON>", "themeModeDark": "Sombre", "themeSeedColor": "Couleur de base du thème", "editMessage": "Modifier le message", "settingsTitle": "Paramètres", "aiSettings": "Paramètres de {aiType}", "userSettings": "Paramètres utilisateur", "assistantSettings": "Paramètres de l'assistant", "systemSettings": "Paramètres système", "systemPrompt": "Prompt système", "clearChats": "Effacer les <PERSON>", "resetSettings": "Réinitialiser les paramètres", "clearCache": "Vider le cache", "aboutTitle": "À propos", "aboutContent": "Maid est une application multiplateforme gratuite et open-source permettant d'interagir avec les modèles Llama.cpp en local et à distance avec Ollama, <PERSON><PERSON><PERSON>, Google Gemini et OpenAI. Maid prend en charge les cartes de personnages Sillytavern pour interagir avec vos personnages préférés. Vous pouvez télécharger une liste de modèles sélectionnés directement depuis Huggingface. Maid est distribuée sous la licence MIT et est fournie sans aucune garantie, explicite ou implicite. Maid n'est affiliée à Huggingface, Meta (Facebook), MistralAI, OpenAI, Google, Microsoft ou toute autre entreprise proposant un modèle compatible avec cette application.", "leadMaintainer": "Responsable principal", "apiKey": "Clé API", "baseUrl": "URL de base", "scrollToRecent": "Faire défiler vers le récent", "clearPrompt": "Effacer le prompt", "submitPrompt": "Envoyer le prompt", "stopPrompt": "<PERSON><PERSON><PERSON><PERSON> le prompt", "typeMessage": "Tapez un message...", "code": "Code", "copyLabel": "Copier {label}", "labelCopied": "{label} copié dans le presse-papiers!", "debugTitle": "Débogage", "warning": "Avertissement", "nsfwWarning": "Ce modèle a été intentionnellement conçu pour produire du contenu NSFW. <PERSON><PERSON> peut inclure du contenu sexuel explicite ou violent impliquant de la torture, du viol, du meurtre et/ou des comportements sexuellement déviants. Si vous êtes sensible à ces sujets, ou si leur discussion enfreint les lois locales, NE CONTINUEZ PAS.", "login": "Se connecter", "logout": "Déconnexion", "register": "S'inscrire", "email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "resetCode": "Code de réinitialisation", "resetCodeSent": "Un code de réinitialisation a été envoyé à votre email.", "sendResetCode": "Envoyer le code de réinitialisation", "sendAgain": "<PERSON><PERSON><PERSON>", "required": "Requis", "invalidEmail": "Veuillez entrer un email valide", "invalidUserName": "Doit contenir 3 à 24 caractères, alphanumériques ou soulignés", "invalidPasswordLength": "Minimum 8 caractères", "invalidPassword": "Inclure majuscules, minuscules, chiffres et symboles", "passwordNoMatch": "Les mots de passe ne correspondent pas", "createAccount": "<PERSON><PERSON><PERSON> un compte", "resetPassword": "Réinitialiser le mot de passe", "backToLogin": "Retour à la connexion", "alreadyHaveAccount": "J'ai déjà un compte", "success": "Su<PERSON>ès", "registrationSuccess": "Inscription réussie", "resetSuccess": "Votre mot de passe a été réinitialisé avec succès.", "emailVerify": "Veuillez vérifier votre email pour la vérification."}