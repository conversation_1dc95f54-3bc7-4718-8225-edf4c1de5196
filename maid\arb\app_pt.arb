{"@@locale": "pt", "friendlyName": "Português", "localeTitle": "Local", "defaultLocale": "Local Padrão", "loading": "Carregando...", "loadModel": "<PERSON><PERSON><PERSON>", "downloadModel": "Baixar Modelo", "noModelSelected": "Nenhum Modelo Selecionado", "noModelLoaded": "<PERSON><PERSON><PERSON>", "localModels": "Modelos Lo<PERSON>is", "size": "<PERSON><PERSON><PERSON>", "parameters": "Parametros", "delete": "<PERSON><PERSON><PERSON>", "select": "Selecionar", "import": "Importar", "export": "Exportar", "edit": "<PERSON><PERSON>", "regenerate": "<PERSON>-gerar", "chatsTitle": "Conversas", "newChat": "Nova Conversa", "anErrorOccurred": "Ocorreu um erro", "errorTitle": "Erro", "key": "Chave", "value": "Valor", "ok": "OK", "proceed": "Proceed", "done": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saveLabel": "<PERSON><PERSON> {label}", "selectTag": "Selecionar Tag", "next": "Próximo", "previous": "Anterior", "contentShared": "<PERSON><PERSON><PERSON><PERSON>", "setUserImage": "Definir Imagem do Usuário", "setAssistantImage": "Definir Imagem do Assistente", "loadUserImage": "Carregar Imagem do Usuário", "loadAssistantImage": "Carregar Imagem do Assistente", "userName": "Nome do Usuário", "assistantName": "Nome do Assistente", "user": "<PERSON><PERSON><PERSON><PERSON>", "assistant": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "aiEcosystem": "Ecosistema da IA", "llamaCpp": "Llama CPP", "llamaCppModel": "Modelo do Llama CPP", "remoteModel": "<PERSON><PERSON>", "refreshRemoteModels": "At<PERSON><PERSON><PERSON>", "ollama": "Ollama", "searchLocalNetwork": "Procurar na Rede Local", "localNetworkSearchTitle": "Busca na Rede Local", "localNetworkSearchContent": "Esta função requer permissões adicionais para procurar na sua rede local por instâncias do Ollama.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Parametros do Modelo", "addParameter": "<PERSON><PERSON><PERSON><PERSON>", "removeParameter": "Remover Parametro", "saveParameters": "<PERSON><PERSON>", "importParameters": "Importar Parametros", "exportParameters": "Exportar Parametros", "selectAiEcosystem": "Selecionar Ecosistema de IA", "selectRemoteModel": "Selecionar Modelo <PERSON>", "selectThemeMode": "Selecionar Tema do APP", "themeMode": "Modo do Tema", "themeModeSystem": "Sistema", "themeModeLight": "<PERSON><PERSON><PERSON>", "themeModeDark": "Escuro", "themeSeedColor": "Cor primária do tema", "editMessage": "<PERSON><PERSON>", "settingsTitle": "Configurações", "aiSettings": "Configurações do {aiType} ", "userSettings": "Configurações do Usuário", "assistantSettings": "Configurações do Assistente", "systemSettings": "Configurações do Sistema", "systemPrompt": "Prompt do Sistema", "clearChats": "Apagar Conversas", "resetSettings": "Voltar a Configuração Padrão", "clearCache": "<PERSON><PERSON><PERSON>", "aboutTitle": "Sobre", "aboutContent": "Maid é um aplicativo gratuito e de código aberto, multiplataforma, para interagir com modelos do llama.cpp localmente, e com os modelos da Ollama, Mistral e OpenAI de forma remota. O Maid é compatível com os cartões de personagens do SillyTavern, permitindo que você interaja com todos os seus personagens favoritos. Ele também permite o download, direto pelo aplicativo, de uma lista selecionada de modelos hospedados no Hugging Face.", "leadMaintainer": "Responsável Principal pelo <PERSON>jeto", "apiKey": "<PERSON><PERSON> da <PERSON>", "baseUrl": "Base URL", "scrollToRecent": "Scroll to Recent", "clearPrompt": "Apagar Prompt", "submitPrompt": "Enviar Prompt", "stopPrompt": "<PERSON>gar Prompt", "typeMessage": "Escreva uma Mensagem...", "code": "Código", "copyLabel": "Copiar {label}", "labelCopied": "{label} Copiado para a Área de Transferência!", "debugTitle": "<PERSON><PERSON><PERSON>", "warning": "Aviso", "nsfwWarning": "Este modelo foi intencionalmente projetado para gerar conteúdo impróprio (NSFW). <PERSON><PERSON> pode incluir conteúdo sexual ou violento explícito envolvendo tortura, estupro, assassinato e/ou comportamentos sexualmente desviantes. Se você for sensível a esses temas ou se a discussão desses temas violar leis locais, NÃO PROSSIGA.", "login": "Entrar", "logout": "<PERSON><PERSON>", "register": "Resgistre-se", "email": "Email", "password": "<PERSON><PERSON>", "confirmPassword": "Confirm<PERSON> a <PERSON>", "resetCode": "Código de recuperação", "resetCodeSent": "Um Código de Reinicio foi enviado para o seu email.", "sendResetCode": "Enviar código de recuperação", "sendAgain": "Enviar De Novo", "required": "Obrigatório", "invalidEmail": "Por favor digite um email válido", "invalidUserName": "Deve ter de 3 a 24 caracteres, alfanumérico e underline", "invalidPasswordLength": "Mínimo de 8 Caracteres", "invalidPassword": "Inclua Letras Minúsculas, Maiúsculas, Números e Símbolos", "passwordNoMatch": "As senhas estão diferentes", "createAccount": "Crie uma conta", "resetPassword": "<PERSON><PERSON><PERSON> a Senha", "backToLogin": "Voltar ao Login", "alreadyHaveAccount": "<PERSON><PERSON> tenho uma conta", "success": "Sucesso", "registrationSuccess": "Resgistrado com Sucesso", "resetSuccess": "Sua senha foi reiniciada com sucesso.", "emailVerify": "Por favor verifique o seu email para concluir a verificação."}