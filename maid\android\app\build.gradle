plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def getCommitCount = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-list', '--count', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim().toInteger()
}

def flutterVersionCode = getCommitCount()

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace = "com.danemadsen.maid"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    defaultConfig {
        applicationId = "com.danemadsen.maid"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName

        // Conditionally apply ABI filters for App Bundle builds
        if (project.gradle.startParameter.taskNames.any { it.contains("bundle") }) {
            ndk {
                abiFilters 'x86_64', 'arm64-v8a'
            }
        }
    }

    signingConfigs {
        release {
            if (keystoreProperties['storeFile']) {
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
                keyAlias keystoreProperties['releaseAlias']
                keyPassword keystoreProperties['releasePassword']
            } else {
                throw new GradleException("Keystore file not found or not configured in key.properties")
            }
        }
        debug {
            if (keystoreProperties['storeFile']) {
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
                keyAlias keystoreProperties['releaseAlias']
                keyPassword keystoreProperties['releasePassword']
            } else {
                throw new GradleException("Keystore file not found or not configured in key.properties")
            }
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.release
        }
        debug {
            signingConfig = signingConfigs.debug
        }
    }

    splits {
        abi {
            enable true
            reset()
            include 'x86_64', 'arm64-v8a'
            exclude 'armeabi-v7a'
            universalApk true
        }
    }

    dependenciesInfo {
        // Disables dependency metadata when building APKs.
        includeInApk = false
        // Disables dependency metadata when building Android App Bundles.
        includeInBundle = false
    }
}

flutter {
    source = "../.."
}

ext.abiCodes = ["x86_64": 1, "arm64-v8a": 2]
import com.android.build.OutputFile
android.applicationVariants.all { variant ->
  variant.outputs.each { output ->
    def abiVersionCode = project.ext.abiCodes.get(output.getFilter(OutputFile.ABI))
    if (abiVersionCode != null) {
      output.versionCodeOverride = variant.versionCode * 10 + abiVersionCode
    }
  }
}