{"@@locale": "tr", "friendlyName": "Türkçe", "localeTitle": "<PERSON><PERSON>", "defaultLocale": "Varsayılan <PERSON>", "loading": "Yükleniyor...", "loadModel": "<PERSON><PERSON>", "downloadModel": "<PERSON><PERSON>", "noModelSelected": "<PERSON> Seçil<PERSON><PERSON>", "noModelLoaded": "<PERSON>", "localModels": "<PERSON><PERSON>", "size": "<PERSON><PERSON>", "parameters": "Parametreler", "delete": "Sil", "select": "Seç", "import": "İçe Aktar", "export": "Dışa Aktar", "edit": "<PERSON><PERSON><PERSON><PERSON>", "regenerate": "Yeniden Oluştur", "chatsTitle": "<PERSON><PERSON><PERSON><PERSON>", "newChat": "<PERSON><PERSON>", "anErrorOccurred": "<PERSON>ir hata o<PERSON>", "errorTitle": "<PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "ok": "<PERSON><PERSON>", "proceed": "<PERSON><PERSON>", "done": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "save": "<PERSON><PERSON>", "saveLabel": "{label} <PERSON><PERSON>", "selectTag": "Etiket Seç", "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON>", "contentShared": "İçerik Paylaşıldı", "setUserImage": "Kullanıcı Resmini <PERSON>", "setAssistantImage": "Asistan <PERSON><PERSON>", "loadUserImage": "Kullanıcı Resmini <PERSON>", "loadAssistantImage": "Asistan <PERSON><PERSON><PERSON>", "userName": "Kullanıcı Adı", "assistantName": "Asistan Adı", "user": "Kullanıcı", "assistant": "Asistan", "cancel": "İptal", "aiEcosystem": "Yapay Zeka Ekosistemi", "llamaCpp": "Llama CPP", "llamaCppModel": "Llama CPP Modeli", "remoteModel": "Uzak Model", "refreshRemoteModels": "Uzak Modelleri Yenile", "ollama": "Ollama", "searchLocalNetwork": "<PERSON><PERSON>", "localNetworkSearchTitle": "<PERSON><PERSON>", "localNetworkSearchContent": "<PERSON><PERSON>, yer<PERSON> ağınızda Ollama örneklerini aramak için ek izinler gerektirir.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Model Parametreleri", "addParameter": "Parametre Ekle", "removeParameter": "Parametreyi <PERSON>", "saveParameters": "<PERSON>met<PERSON><PERSON>", "importParameters": "Parametreleri İçe Aktar", "exportParameters": "Parametreleri Dışa Aktar", "selectAiEcosystem": "Yapay Zeka Ekosistemini Seç", "selectRemoteModel": "Uzak Modeli Seç", "selectThemeMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "themeMode": "<PERSON><PERSON>", "themeModeSystem": "Sistem", "themeModeLight": "Açık", "themeModeDark": "<PERSON><PERSON>", "themeSeedColor": "<PERSON><PERSON>", "editMessage": "Mesajı <PERSON>", "settingsTitle": "<PERSON><PERSON><PERSON>", "aiSettings": "{aiType} Ayarları", "userSettings": "Kullanıcı Ayarları", "assistantSettings": "Asistan Ayarları", "systemSettings": "Sistem Ayarları", "systemPrompt": "Sistem İstemi", "clearChats": "So<PERSON><PERSON><PERSON><PERSON>", "resetSettings": "Ayarları Sıfırla", "clearCache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aboutTitle": "Hakkında", "aboutContent": "Maid, llama.cpp modelleriyle yerel o<PERSON> ve Ollama, Mistral ve OpenAI modelleriyle uzaktan etkileşim kurmak için çapraz platformlu, ücretsiz ve açık kaynaklı bir uygulamadır. Maid, sillytavern karakter kartlarını destekleyerek favori karakterlerinizle etkileşim kurmanızı sağlar. Maid, huggingface’ten doğrudan uygulama içinde küratörlü bir model listesini indirmeyi destekler. Maid, MIT lisansı altında dağıtılır ve herhangi bir garanti olmaksızın, açık veya zımni olarak sunulur. Maid, Huggingface, Meta (Facebook), MistralAi, OpenAI, Google, Microsoft veya bu uygulama ile uyumlu bir model sağlayan herhangi bir şirketle bağlantılı değildir.", "leadMaintainer": "Baş Sorumlu", "apiKey": "API Anahtarı", "baseUrl": "Temel URL", "scrollToRecent": "<PERSON>", "clearPrompt": "<PERSON><PERSON><PERSON>", "submitPrompt": "<PERSON><PERSON><PERSON>", "stopPrompt": "<PERSON><PERSON><PERSON>", "typeMessage": "Bir mesaj yazın...", "code": "Kod", "copyLabel": "{label} Ko<PERSON><PERSON>", "labelCopied": "{label} panoya kopyalandı!", "debugTitle": "Debug", "warning": "Uyarı", "nsfwWarning": "Bu model, NSFW (uygunsuz) içerik üretmek üzere kasıtlı olarak tasarlanmıştır. Bu, işkence, tecavüz, cinayet ve/veya cinsel sapkın davranışlar içeren açık cinsel veya şiddet içeriğini kapsayabilir. Bu tür konulara karşı hassassanız veya bu konuların tartışılması yerel yasalara aykırıysa, DEVAM ETMEYİN.", "login": "<PERSON><PERSON><PERSON>", "logout": "Çıkış Yap", "register": "<PERSON><PERSON><PERSON>", "email": "E-posta", "password": "Şifre", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "resetCode": "Sıfırlama Kodu", "resetCodeSent": "E-posta adresinize bir sıfırlama kodu gönderildi.", "sendResetCode": "Sıfırlama Kodunu Gönder", "sendAgain": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON>", "invalidEmail": "Lütfen geçerli bir e-posta adresi girin", "invalidUserName": "3-24 <PERSON><PERSON><PERSON>, alfanümerik veya alt çizgi", "invalidPasswordLength": "Minimum 8 karakter", "invalidPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> harf, <PERSON><PERSON><PERSON><PERSON><PERSON> harf, rakam ve sembol içermelidir", "passwordNoMatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "createAccount": "<PERSON><PERSON><PERSON>", "resetPassword": "Şifreyi <PERSON>", "backToLogin": "<PERSON><PERSON><PERSON><PERSON>", "alreadyHaveAccount": "Zaten bir hesabım var", "success": "Başarılı", "registrationSuccess": "Kayıt Başarılı", "resetSuccess": "Şifreniz başarıyla sıfırlandı.", "emailVerify": "Lütfen doğrulama için e-posta adresinizi kontrol edin."}