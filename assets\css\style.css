/* Reset e Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Cores principais baseadas no site original */
    --primary-color: #d32f2f;
    --primary-dark: #b71c1c;
    --secondary-color: #ff6f00;
    --accent-color: #4caf50;
    --text-dark: #212121;
    --text-light: #757575;
    --text-white: #ffffff;
    --background-light: #fafafa;
    --background-white: #ffffff;
    --border-color: #e0e0e0;
    --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 16px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 32px rgba(0,0,0,0.2);
    
    /* Typography */
    --font-family: 'Poppins', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--background-white);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.btn-full {
    width: 100%;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--background-white);
    box-shadow: var(--shadow-light);
    z-index: 1000;
}

.navbar {
    padding: var(--spacing-md) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo {
    height: 40px;
    width: auto;
}

.brand-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.cart-btn {
    position: relative;
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.cart-btn:hover {
    background-color: var(--background-light);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: var(--text-white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.menu-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--text-dark);
    transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: -2;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../images/pizza-pattern.png');
    background-size: 200px;
    opacity: 0.1;
    z-index: -1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: var(--text-white);
    max-width: 800px;
    margin: 0 auto;
    padding-top: 80px;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    color: rgba(255, 255, 255, 0.9);
}

.hero-cta {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.hero-info {
    display: flex;
    gap: var(--spacing-xl);
    justify-content: center;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.9);
}

.info-item i {
    font-size: var(--font-size-lg);
    color: var(--secondary-color);
}

/* Section Styles */
section {
    padding: var(--spacing-3xl) 0;
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-dark);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: var(--radius-sm);
}

/* Highlights Section */
.highlights {
    background-color: var(--background-light);
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.highlight-card {
    background-color: var(--background-white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.highlight-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-medium);
}

.highlight-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.highlight-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.highlight-card:hover .highlight-image img {
    transform: scale(1.1);
}

.highlight-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background-color: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.highlight-content {
    padding: var(--spacing-lg);
}

.highlight-content h3 {
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.highlight-content p {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-lg);
}

.highlight-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

/* Menu Section */
.menu-categories {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.category-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 2px solid var(--border-color);
    background-color: var(--background-white);
    color: var(--text-light);
    border-radius: var(--radius-full);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.category-btn:hover,
.category-btn.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: var(--text-white);
}

.menu-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.menu-item {
    background-color: var(--background-white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.menu-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.menu-item-image {
    height: 180px;
    overflow: hidden;
}

.menu-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.menu-item-content {
    padding: var(--spacing-lg);
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.menu-item-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.menu-item-price {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.menu-item-description {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
}

.menu-item-sizes {
    margin-bottom: var(--spacing-lg);
}

.size-options {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.size-option {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.size-option:hover,
.size-option.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: var(--text-white);
}

.menu-item-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background-color: var(--background-white);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.quantity-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: var(--spacing-xs);
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-features {
    display: flex;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-dark);
}

.feature i {
    color: var(--accent-color);
    font-size: var(--font-size-lg);
}

.about-image {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

/* Testimonials */
.testimonials {
    background-color: var(--background-light);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.testimonial-card {
    background-color: var(--background-white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    text-align: center;
}

.testimonial-stars {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: var(--spacing-lg);
    color: var(--text-dark);
}

.testimonial-author strong {
    color: var(--text-dark);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.testimonial-author span {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
}

.contact-item {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.contact-item i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    width: 40px;
    flex-shrink: 0;
}

.contact-item h4 {
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.contact-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background-color: var(--text-dark);
    color: var(--text-white);
    padding: var(--spacing-3xl) 0 var(--spacing-xl) 0;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo {
    height: 40px;
    margin-bottom: var(--spacing-md);
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
}

.footer-links h4 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-white);
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--text-white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--text-white);
    border-radius: var(--radius-full);
    text-decoration: none;
    transition: background-color var(--transition-fast);
}

.social-links a:hover {
    background-color: var(--primary-dark);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-xl);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--background-white);
    border-radius: var(--radius-lg);
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--text-light);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-dark);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.cart-total {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-xl);
    color: var(--text-dark);
}

/* Cart Item Styles */
.cart-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-details {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.cart-item-size {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.cart-item-price {
    font-weight: 600;
    color: var(--primary-color);
}

.cart-item-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.cart-remove-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.cart-remove-btn:hover {
    color: var(--primary-color);
}

/* Empty Cart */
.cart-empty {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-light);
}

.cart-empty i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    color: var(--border-color);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.align-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* Additional Menu Item Styles */
.menu-item-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background-color: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 1;
}

.menu-item-badge.promotion {
    background-color: var(--secondary-color);
}

.original-price {
    text-decoration: line-through;
    color: var(--text-light);
    font-size: var(--font-size-sm);
    margin-right: var(--spacing-sm);
}

.current-price {
    color: var(--primary-color);
    font-weight: 700;
}

/* Loading States */
.loading-menu {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-light);
    font-size: var(--font-size-lg);
}

.no-items {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-light);
}

/* Header Scrolled State */
.header.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-medium);
}

/* Add to Cart Feedback */
.add-to-cart-feedback {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--accent-color);
    color: var(--text-white);
    padding: 12px 20px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-medium);
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

/* Success Actions */
.success-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--text-white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: 1001;
}

.skip-link:focus {
    top: 6px;
}
