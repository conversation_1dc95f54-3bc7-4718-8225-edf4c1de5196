# 🍕 Painel Administrativo - Laricas Delivery

Sistema completo de administração para o site da Laricas Delivery, desenvolvido em PHP com interface moderna e intuitiva.

## 📋 Características Principais

### 🔐 Sistema de Autenticação
- Login seguro com validação de credenciais
- Controle de sessões com timeout automático
- Diferentes níveis de permissão (Admin, Manager)
- Logs de atividades do usuário

### 📊 Dashboard Completo
- Visão geral de vendas e pedidos
- Gráficos interativos de performance
- Estatísticas em tempo real
- Indicadores de crescimento

### 🛒 Gerenciamento de Pedidos
- Listagem completa de pedidos
- Filtros por status, data e cliente
- Atualização de status em tempo real
- Detalhes completos do pedido
- Notificações automáticas para clientes

### 🍕 Gerenciamento de Produtos
- CRUD completo de produtos
- Upload e redimensionamento de imagens
- Múltiplos tamanhos e preços
- Categorização avançada
- Controle de estoque

### 🏷️ Sistema de Categorias
- Organização hierárquica
- Ícones personalizados
- Ordenação customizável
- Status ativo/inativo

### 🎫 Cupons e Promoções
- Criação de cupons de desconto
- Promoções por categoria/produto
- Controle de validade e uso
- Relatórios de utilização

### ⚙️ Configurações Avançadas
- Configurações gerais do sistema
- Horários de funcionamento
- Métodos de pagamento
- Áreas de entrega
- Personalização visual

### 📈 Relatórios e Analytics
- Relatórios de vendas
- Produtos mais vendidos
- Performance por período
- Exportação de dados

## 🚀 Instalação

### 1. Requisitos do Sistema
- PHP 7.4 ou superior
- MySQL 5.7 ou superior
- Extensões PHP: PDO, PDO_MySQL, GD, JSON
- Servidor web (Apache/Nginx)

### 2. Configuração do Banco de Dados
```sql
-- Criar banco de dados
CREATE DATABASE banco0508 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Configurar usuário (se necessário)
GRANT ALL PRIVILEGES ON banco0508.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Instalação Automática
1. Acesse `admin/install.php` no navegador
2. Siga o assistente de instalação
3. Teste a conexão com o banco
4. Execute a instalação das tabelas
5. Acesse o painel com as credenciais padrão

### 4. Credenciais Padrão
- **Usuário:** admin
- **Senha:** password

⚠️ **Importante:** Altere a senha padrão após o primeiro login!

## 📁 Estrutura de Arquivos

```
admin/
├── api/                    # APIs REST
│   ├── dashboard.php       # API do dashboard
│   ├── orders.php         # API de pedidos
│   ├── products.php       # API de produtos
│   ├── categories.php     # API de categorias
│   ├── coupons.php        # API de cupons
│   ├── settings.php       # API de configurações
│   └── upload.php         # API de upload
├── config/                 # Configurações
│   ├── database.php       # Configuração do banco
│   └── config.example.php # Exemplo de configuração
├── database/              # Scripts do banco
│   ├── schema.sql         # Estrutura das tabelas
│   └── initial_data.sql   # Dados iniciais
├── includes/              # Arquivos incluídos
│   ├── auth.php          # Sistema de autenticação
│   ├── functions.php     # Funções auxiliares
│   ├── header.php        # Cabeçalho das páginas
│   └── footer.php        # Rodapé das páginas
├── uploads/               # Arquivos enviados
│   ├── products/         # Imagens de produtos
│   ├── categories/       # Imagens de categorias
│   └── banners/          # Banners e imagens gerais
├── dashboard.php          # Página principal
├── orders.php            # Gerenciamento de pedidos
├── products.php          # Gerenciamento de produtos
├── categories.php        # Gerenciamento de categorias
├── coupons.php           # Gerenciamento de cupons
├── settings.php          # Configurações do sistema
├── login.php             # Página de login
├── logout.php            # Logout
├── install.php           # Instalador
└── README.md             # Este arquivo
```

## 🔧 Configuração Avançada

### Personalização de Cores
Edite as variáveis CSS no arquivo `includes/header.php`:
```css
:root {
    --primary-color: #d32f2f;      /* Cor principal */
    --secondary-color: #ff6f00;    /* Cor secundária */
    --accent-color: #4caf50;       /* Cor de destaque */
}
```

### Configuração de Upload
Ajuste as configurações em `config/config.php`:
```php
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
```

### Configuração de Email
Para notificações por email, configure:
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'sua-senha-app');
```

## 📱 APIs Disponíveis

### Dashboard
- `GET /api/dashboard.php?type=overview` - Estatísticas gerais
- `GET /api/dashboard.php?type=sales&period=7days` - Dados de vendas
- `GET /api/dashboard.php?type=orders` - Dados de pedidos
- `GET /api/dashboard.php?type=products` - Produtos mais vendidos

### Pedidos
- `GET /api/orders.php` - Listar pedidos
- `GET /api/orders.php?id=123` - Detalhes do pedido
- `PUT /api/orders.php?id=123` - Atualizar status
- `POST /api/orders.php` - Criar pedido
- `DELETE /api/orders.php?id=123` - Deletar pedido

### Produtos
- `GET /api/products.php` - Listar produtos
- `GET /api/products.php?id=123` - Detalhes do produto
- `POST /api/products.php` - Criar produto
- `PUT /api/products.php?id=123` - Atualizar produto
- `DELETE /api/products.php?id=123` - Deletar produto

### Upload
- `POST /api/upload.php` - Upload de arquivos

## 🛡️ Segurança

### Medidas Implementadas
- Validação e sanitização de dados
- Proteção contra SQL Injection
- Proteção contra XSS
- Controle de sessões
- Logs de atividades
- Rate limiting (configurável)

### Recomendações
1. Use HTTPS em produção
2. Mantenha o PHP atualizado
3. Configure backups regulares
4. Monitore logs de erro
5. Use senhas fortes
6. Limite acesso por IP (se necessário)

## 📊 Banco de Dados

### Tabelas Principais
- `admins` - Usuários administrativos
- `categories` - Categorias de produtos
- `products` - Produtos do cardápio
- `product_sizes` - Tamanhos e preços
- `orders` - Pedidos dos clientes
- `order_items` - Itens dos pedidos
- `customers` - Dados dos clientes
- `coupons` - Cupons de desconto
- `promotions` - Promoções ativas
- `settings` - Configurações do sistema
- `activity_logs` - Logs de atividades

### Backup e Restauração
```bash
# Backup
mysqldump -u root -p banco0508 > backup_$(date +%Y%m%d).sql

# Restauração
mysql -u root -p banco0508 < backup_20241201.sql
```

## 🔄 Atualizações

### Verificar Versão
A versão atual é exibida no rodapé da página de login.

### Aplicar Atualizações
1. Faça backup do banco de dados
2. Substitua os arquivos do sistema
3. Execute scripts de migração (se houver)
4. Teste todas as funcionalidades

## 🐛 Solução de Problemas

### Problemas Comuns

**Erro de Conexão com Banco:**
- Verifique as credenciais em `config/database.php`
- Confirme se o MySQL está rodando
- Teste a conexão manualmente

**Erro de Upload:**
- Verifique permissões da pasta `uploads/`
- Confirme o tamanho máximo do arquivo
- Verifique extensões permitidas

**Sessão Expira Rapidamente:**
- Ajuste `SESSION_TIMEOUT` em `config/config.php`
- Verifique configurações do PHP

**Gráficos Não Carregam:**
- Verifique console do navegador
- Confirme se Chart.js está carregando
- Teste APIs manualmente

### Logs de Erro
Os logs são salvos em:
- `logs/php_errors.log` - Erros do PHP
- `logs/application.log` - Logs da aplicação
- Tabela `activity_logs` - Atividades dos usuários

## 📞 Suporte

Para dúvidas ou problemas:
1. Consulte esta documentação
2. Verifique os logs de erro
3. Teste em ambiente de desenvolvimento
4. Verifique as configurações do servidor

## 📄 Licença

Este sistema foi desenvolvido especificamente para a Laricas Delivery. Todos os direitos reservados.

---

**Desenvolvido com ❤️ para facilitar a gestão do seu delivery**
