{"@@locale": "es", "friendlyName": "Español", "localeTitle": "Idioma", "defaultLocale": "Idioma predeterminado", "loading": "Cargando...", "loadModel": "<PERSON><PERSON> modelo", "downloadModel": "<PERSON><PERSON><PERSON> modelo", "noModelSelected": "Ningún modelo selecci<PERSON>do", "noModelLoaded": "Ningún modelo cargado", "localModels": "Modelos locales", "size": "<PERSON><PERSON><PERSON>", "parameters": "Parámetros", "delete": "Eliminar", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "import": "Importar", "export": "Exportar", "edit": "<PERSON><PERSON>", "regenerate": "<PERSON><PERSON><PERSON>", "chatsTitle": "Chats", "newChat": "Nuevo chat", "anErrorOccurred": "Ocurrió un error", "errorTitle": "Error", "key": "Clave", "value": "Valor", "ok": "OK", "proceed": "Proceder", "done": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "save": "Guardar", "saveLabel": "Guardar {label}", "selectTag": "Seleccionar etiqueta", "next": "Siguient<PERSON>", "previous": "Anterior", "contentShared": "Contenido compartido", "setUserImage": "<PERSON><PERSON><PERSON> de usuario", "setAssistantImage": "<PERSON><PERSON><PERSON> image<PERSON> de as<PERSON>", "loadUserImage": "<PERSON><PERSON> imagen de usuario", "loadAssistantImage": "<PERSON>gar imagen de asistente", "userName": "Nombre de usuario", "assistantName": "Nombre del asistente", "user": "Usuario", "assistant": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "aiEcosystem": "Ecosistema IA", "llamaCpp": "Llama CPP", "llamaCppModel": "Modelo Llama CPP", "remoteModel": "<PERSON><PERSON>", "refreshRemoteModels": "Actualizar modelos remotos", "ollama": "Ollama", "searchLocalNetwork": "Buscar en la red local", "localNetworkSearchTitle": "Búsqueda en la red local", "localNetworkSearchContent": "Esta función requiere permisos adicionales para buscar instancias de Ollama en su red local.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Parámetros del modelo", "addParameter": "<PERSON><PERSON><PERSON>", "removeParameter": "Eliminar parámetro", "saveParameters": "Guardar parámetros", "importParameters": "Importar parámet<PERSON>", "exportParameters": "Exportar parámetros", "selectAiEcosystem": "Seleccionar ecosistema de IA", "selectRemoteModel": "Seleccionar modelo remoto", "selectThemeMode": "Seleccionar modo de tema", "themeMode": "<PERSON><PERSON> de <PERSON>", "themeModeSystem": "Sistema", "themeModeLight": "<PERSON><PERSON><PERSON>", "themeModeDark": "Oscuro", "themeSeedColor": "Color base del tema", "editMessage": "<PERSON><PERSON>", "settingsTitle": "Configuración", "aiSettings": "Configuración de {aiType}", "userSettings": "Configuración de usuario", "assistantSettings": "Configuración del asistente", "systemSettings": "Configuración del sistema", "systemPrompt": "Prompt del sistema", "clearChats": "<PERSON><PERSON><PERSON> chats", "resetSettings": "Restablecer configuración", "clearCache": "Lim<PERSON><PERSON> caché", "aboutTitle": "Acerca de", "aboutContent": "Maid es una aplicación gratuita y de código abierto multiplataforma para interactuar con modelos Llama.cpp localmente y de forma remota con Ollama, Mistral, Google Gemini y OpenAI. Maid admite tarjetas de personajes de Sillytavern para que puedas interactuar con tus personajes favoritos. También permite descargar una lista seleccionada de modelos directamente desde Huggingface. Maid se distribuye bajo la licencia MIT y se proporciona sin garantía de ningún tipo, expresa o implícita. Maid no está afiliada con Huggingface, Meta (Facebook), MistralAI, OpenAI, Google, Microsoft ni ninguna otra empresa que proporcione modelos compatibles con esta aplicación.", "leadMaintainer": "Mantenedor principal", "apiKey": "Clave API", "baseUrl": "URL base", "scrollToRecent": "Desplazarse a reciente", "clearPrompt": "<PERSON><PERSON><PERSON> prompt", "submitPrompt": "Enviar prompt", "stopPrompt": "Detener prompt", "typeMessage": "Escribe un mensaje...", "code": "Código", "copyLabel": "Copiar {label}", "labelCopied": "¡{label} copiado al portapapeles!", "debugTitle": "Depuración", "warning": "Advertencia", "nsfwWarning": "Este modelo ha sido diseñado intencionadamente para generar contenido NSFW. Esto puede incluir contenido sexual explícito o violento que involucre tortura, violación, asesinato y/o comportamientos sexualmente desviados. Si eres sensible a estos temas o su discusión infringe las leyes locales, NO CONTINÚES.", "login": "In<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "register": "Registrarse", "email": "Correo electrónico", "password": "Contraseña", "confirmPassword": "Confirmar con<PERSON>", "resetCode": "Código de reinicio", "resetCodeSent": "Se ha enviado un código de reinicio a su correo electrónico.", "sendResetCode": "Enviar código de reinicio", "sendAgain": "Enviar de nuevo", "required": "Requerido", "invalidEmail": "Por favor, introduce un correo electrónico válido", "invalidUserName": "De 3 a 24 caracteres, alfanuméricos o guion bajo", "invalidPasswordLength": "Mínimo 8 caracteres", "invalidPassword": "Incluye <PERSON>s, minúsculas, número y símbolo", "passwordNoMatch": "Las contraseñas no coinciden", "createAccount": "<PERSON><PERSON><PERSON> una cuenta", "resetPassword": "Restablecer contraseña", "backToLogin": "Volver a iniciar sesión", "alreadyHaveAccount": "Ya tengo una cuenta", "success": "Éxito", "registrationSuccess": "<PERSON><PERSON>oso", "resetSuccess": "Tu contraseña ha sido restablecida con éxito.", "emailVerify": "Por favor, revisa tu correo para verificar."}