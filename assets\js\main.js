// Main JavaScript File - Laricas Delivery
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeNavigation();
    initializeMenu();
    initializeScrollEffects();
    initializeContactForm();
    loadHighlights();
});

// Navigation functionality
function initializeNavigation() {
    const menuToggle = document.getElementById('menu-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (menuToggle && navMenu) {
        menuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            menuToggle.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navMenu.classList.remove('active');
            menuToggle.classList.remove('active');
        });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navMenu.contains(e.target) && !menuToggle.contains(e.target)) {
            navMenu.classList.remove('active');
            menuToggle.classList.remove('active');
        }
    });

    // Active navigation link based on scroll
    window.addEventListener('scroll', updateActiveNavLink);
}

// Update active navigation link based on current section
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    const scrollY = window.pageYOffset;

    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        
        if (scrollY >= sectionTop && scrollY < sectionTop + sectionHeight) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// Menu functionality
function initializeMenu() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    const menuItemsContainer = document.getElementById('menu-items');

    // Category button click handlers
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Load menu items for category
            loadMenuItems(category);
        });
    });

    // Load initial category (pizzas-salgadas)
    loadMenuItems('pizzas-salgadas');
}

// Load menu items for a specific category
function loadMenuItems(category) {
    const menuItemsContainer = document.getElementById('menu-items');
    const items = MenuData.getMenuItemsByCategory(category);

    if (!menuItemsContainer) return;

    // Show loading state
    menuItemsContainer.innerHTML = '<div class="loading-menu">Carregando...</div>';

    // Simulate loading delay for better UX
    setTimeout(() => {
        if (items.length === 0) {
            menuItemsContainer.innerHTML = '<div class="no-items">Nenhum item encontrado nesta categoria.</div>';
            return;
        }

        menuItemsContainer.innerHTML = items.map(item => createMenuItemHTML(item)).join('');
        
        // Add event listeners to new buttons
        addMenuItemEventListeners();
    }, 300);
}

// Create HTML for a menu item
function createMenuItemHTML(item) {
    const popularBadge = item.popular ? '<div class="menu-item-badge">Popular</div>' : '';
    const promotionBadge = item.isPromotion ? `<div class="menu-item-badge promotion">${item.promotionText}</div>` : '';
    const originalPrice = item.originalPrice ? `<span class="original-price">R$ ${item.originalPrice.toFixed(2).replace('.', ',')}</span>` : '';

    return `
        <div class="menu-item" data-item-id="${item.id}">
            <div class="menu-item-image">
                <img src="${item.image}" alt="${item.name}" onerror="this.src='assets/images/placeholder.jpg'">
                ${popularBadge}
                ${promotionBadge}
            </div>
            <div class="menu-item-content">
                <div class="menu-item-header">
                    <h3 class="menu-item-title">${item.name}</h3>
                    <div class="menu-item-price">
                        ${originalPrice}
                        <span class="current-price">R$ ${item.sizes[0].price.toFixed(2).replace('.', ',')}</span>
                    </div>
                </div>
                <p class="menu-item-description">${item.description}</p>
                
                ${item.sizes.length > 1 ? createSizeOptionsHTML(item) : ''}
                
                <div class="menu-item-actions">
                    <div class="quantity-controls">
                        <button class="quantity-btn minus-btn" data-action="decrease">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" value="1" min="1" max="10">
                        <button class="quantity-btn plus-btn" data-action="increase">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <button class="btn btn-primary add-to-cart-btn" data-item-id="${item.id}">
                        <i class="fas fa-cart-plus"></i>
                        Adicionar
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Create size options HTML
function createSizeOptionsHTML(item) {
    if (item.sizes.length <= 1) return '';

    return `
        <div class="menu-item-sizes">
            <div class="size-options">
                ${item.sizes.map((size, index) => `
                    <div class="size-option ${index === 0 ? 'active' : ''}" 
                         data-size="${size.size}" 
                         data-price="${size.price}">
                        <span class="size-name">${size.size}</span>
                        <span class="size-price">R$ ${size.price.toFixed(2).replace('.', ',')}</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Add event listeners to menu item elements
function addMenuItemEventListeners() {
    // Size option selection
    document.querySelectorAll('.size-option').forEach(option => {
        option.addEventListener('click', function() {
            const menuItem = this.closest('.menu-item');
            const sizeOptions = menuItem.querySelectorAll('.size-option');
            const priceElement = menuItem.querySelector('.current-price');
            
            // Update active size
            sizeOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            
            // Update displayed price
            const price = parseFloat(this.getAttribute('data-price'));
            priceElement.textContent = `R$ ${price.toFixed(2).replace('.', ',')}`;
        });
    });

    // Quantity controls
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const menuItem = this.closest('.menu-item');
            const quantityInput = menuItem.querySelector('.quantity-input');
            const action = this.getAttribute('data-action');
            let currentValue = parseInt(quantityInput.value);

            if (action === 'increase' && currentValue < 10) {
                quantityInput.value = currentValue + 1;
            } else if (action === 'decrease' && currentValue > 1) {
                quantityInput.value = currentValue - 1;
            }
        });
    });

    // Add to cart buttons
    document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const menuItem = this.closest('.menu-item');
            const itemId = parseInt(this.getAttribute('data-item-id'));
            const quantity = parseInt(menuItem.querySelector('.quantity-input').value);
            const activeSize = menuItem.querySelector('.size-option.active');
            const size = activeSize ? activeSize.getAttribute('data-size') : null;

            // Add loading state
            this.classList.add('loading');
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adicionando...';

            // Simulate API call delay
            setTimeout(() => {
                const success = addToCart(itemId, size, quantity);
                
                // Reset button
                this.classList.remove('loading');
                this.innerHTML = '<i class="fas fa-cart-plus"></i> Adicionar';
                
                if (success) {
                    // Reset quantity to 1
                    menuItem.querySelector('.quantity-input').value = 1;
                }
            }, 500);
        });
    });
}

// Load highlights section
function loadHighlights() {
    const popularItems = MenuData.getPopularItems().slice(0, 3);
    const highlightsGrid = document.querySelector('.highlights-grid');
    
    if (!highlightsGrid || popularItems.length === 0) return;

    highlightsGrid.innerHTML = popularItems.map(item => `
        <div class="highlight-card">
            <div class="highlight-image">
                <img src="${item.image}" alt="${item.name}" onerror="this.src='assets/images/placeholder.jpg'">
                <div class="highlight-badge">${getBadgeText(item)}</div>
            </div>
            <div class="highlight-content">
                <h3>${item.name}</h3>
                <p>${item.description}</p>
                <div class="highlight-price">
                    <span class="price">R$ ${item.sizes[0].price.toFixed(2).replace('.', ',')}</span>
                    <button class="btn btn-sm btn-primary" onclick="addToCart(${item.id})">
                        Adicionar
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Get badge text for highlight items
function getBadgeText(item) {
    if (item.category === 'pizzas-salgadas') return 'Mais Vendida';
    if (item.category === 'pizzas-doces') return 'Especial';
    if (item.category === 'esfihas') return 'Tradicional';
    if (item.category === 'promocoes') return 'Promoção';
    return 'Popular';
}

// Scroll effects
function initializeScrollEffects() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Header background on scroll
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Contact form functionality
function initializeContactForm() {
    const contactForm = document.getElementById('contact-form');
    
    if (!contactForm) return;

    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            message: formData.get('message')
        };

        // Validate form
        if (!validateContactForm(data)) return;

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
        submitBtn.disabled = true;

        // Simulate form submission
        setTimeout(() => {
            // Create WhatsApp message
            let message = `*CONTATO SITE LARICAS DELIVERY*\n\n`;
            message += `*Nome:* ${data.name}\n`;
            message += `*Email:* ${data.email}\n`;
            if (data.phone) message += `*Telefone:* ${data.phone}\n`;
            message += `*Mensagem:* ${data.message}`;

            const encodedMessage = encodeURIComponent(message);
            const whatsappUrl = `https://wa.me/5514999999999?text=${encodedMessage}`;

            // Open WhatsApp
            window.open(whatsappUrl, '_blank');

            // Reset form
            this.reset();
            showSuccessMessage('Mensagem enviada com sucesso!');

            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 1000);
    });
}

// Validate contact form
function validateContactForm(data) {
    const errors = [];

    if (!data.name || data.name.trim().length < 2) {
        errors.push('Nome deve ter pelo menos 2 caracteres');
    }

    if (!data.email || !isValidEmail(data.email)) {
        errors.push('Email inválido');
    }

    if (!data.message || data.message.trim().length < 10) {
        errors.push('Mensagem deve ter pelo menos 10 caracteres');
    }

    if (errors.length > 0) {
        showErrorMessage(errors.join('\n'));
        return false;
    }

    return true;
}

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Show success message
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

// Show error message
function showErrorMessage(message) {
    showMessage(message, 'error');
}

// Show message (generic)
function showMessage(message, type = 'info') {
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="message-close">&times;</button>
    `;

    // Add styles
    Object.assign(messageEl.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: type === 'success' ? 'var(--accent-color)' : type === 'error' ? '#f44336' : 'var(--primary-color)',
        color: 'white',
        padding: '16px 20px',
        borderRadius: '8px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
        zIndex: '1002',
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        maxWidth: '400px',
        fontSize: '14px',
        fontWeight: '500',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease'
    });

    // Close button styles
    const closeBtn = messageEl.querySelector('.message-close');
    Object.assign(closeBtn.style, {
        background: 'none',
        border: 'none',
        color: 'white',
        fontSize: '18px',
        cursor: 'pointer',
        padding: '0',
        marginLeft: 'auto'
    });

    document.body.appendChild(messageEl);

    // Animate in
    setTimeout(() => {
        messageEl.style.transform = 'translateX(0)';
    }, 100);

    // Close button functionality
    closeBtn.addEventListener('click', () => {
        removeMessage(messageEl);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeMessage(messageEl);
    }, 5000);
}

// Remove message
function removeMessage(messageEl) {
    if (messageEl.parentNode) {
        messageEl.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }
}

// Utility functions
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const targetPosition = section.offsetTop - headerHeight;

        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    }
}

function openWhatsApp() {
    const message = encodeURIComponent('Olá! Gostaria de fazer um pedido na Laricas Delivery.');
    const whatsappUrl = `https://wa.me/5514999999999?text=${message}`;
    window.open(whatsappUrl, '_blank');
}

// Search functionality (for future implementation)
function searchMenu(query) {
    const results = MenuData.searchMenuItems(query);
    // Implementation for search results display
    console.log('Search results:', results);
}

// Export functions for global use
window.scrollToSection = scrollToSection;
window.openWhatsApp = openWhatsApp;
window.searchMenu = searchMenu;
