- name: Phi 3 Mini 4K Instruct
  repo: microsoft/Phi-3-mini-4k-instruct-gguf
  branch: main
  parameters: 3.82
  tags:
    Q4: Phi-3-mini-4k-instruct-q4.gguf
    FP16: Phi-3-mini-4k-instruct-fp16.gguf

- name: TinyLlama 1.1B Chat
  repo: TheBloke/TinyLlama-1.1B-Chat-v1.0-GGUF
  branch: main
  parameters: 1.1
  tags:
    Q2_K: tinyllama-1.1b-chat-v1.0.Q2_K.gguf
    Q3_K_L: tinyllama-1.1b-chat-v1.0.Q3_K_L.gguf
    Q3_K_M: tinyllama-1.1b-chat-v1.0.Q3_K_M.gguf
    Q3_K_S: tinyllama-1.1b-chat-v1.0.Q3_K_S.gguf
    Q4_0: tinyllama-1.1b-chat-v1.0.Q4_0.gguf
    Q4_K_M: tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf
    Q4_K_S: tinyllama-1.1b-chat-v1.0.Q4_K_S.gguf
    Q5_0: tinyllama-1.1b-chat-v1.0.Q5_0.gguf
    Q5_K_M: tinyllama-1.1b-chat-v1.0.Q5_K_M.gguf
    Q5_K_S: tinyllama-1.1b-chat-v1.0.Q5_K_S.gguf
    Q6_K: tinyllama-1.1b-chat-v1.0.Q6_K.gguf
    Q8_0: tinyllama-1.1b-chat-v1.0.Q8_0.gguf

- name: Gemma 2 2B IT
  repo: bartowski/gemma-2-2b-it-GGUF
  branch: main
  parameters: 2.0
  tags:
    IQ3_M: gemma-2-2b-it-IQ3_M.gguf
    IQ4_XS: gemma-2-2b-it-IQ4_XS.gguf
    Q3_K_L: gemma-2-2b-it-Q3_K_L.gguf
    Q4_K_M: gemma-2-2b-it-Q4_K_M.gguf
    Q4_K_S: gemma-2-2b-it-Q4_K_S.gguf
    Q5_K_M: gemma-2-2b-it-Q5_K_M.gguf
    Q5_K_S: gemma-2-2b-it-Q5_K_S.gguf
    Q6_K: gemma-2-2b-it-Q6_K.gguf
    Q6_K_L: gemma-2-2b-it-Q6_K_L.gguf
    Q8_0: gemma-2-2b-it-Q8_0.gguf
    F32: gemma-2-2b-it-f32.gguf

- name: Gemma 3 1B IT
  repo: unsloth/gemma-3-1b-it-GGUF
  branch: main
  parameters: 1.0
  tags:
    Q2_K: gemma-3-1b-it-Q2_K.gguf
    Q2_K_L: gemma-3-1b-it-Q2_K_L.gguf
    Q3_K_M: gemma-3-1b-it-Q3_K_M.gguf
    Q4_K_M: gemma-3-1b-it-Q4_K_M.gguf
    Q5_K_M: gemma-3-1b-it-Q5_K_M.gguf
    Q6_K: gemma-3-1b-it-Q6_K.gguf
    Q8_0: gemma-3-1b-it-Q8_0.gguf
    BF16: gemma-3-1b-it-BF16.gguf

- name: Gemmasutra Mini 2B v1
  repo: TheDrummer/Gemmasutra-Mini-2B-v1-GGUF
  branch: main
  parameters: 2.0
  tags:
    BF16: Gemmasutra-Mini-2B-v1-BF16.gguf
    Q2_K: Gemmasutra-Mini-2B-v1-Q2_K.gguf
    Q3_K: Gemmasutra-Mini-2B-v1-Q3_K.gguf
    Q3_K_L: Gemmasutra-Mini-2B-v1-Q3_K_L.gguf
    Q3_K_M: Gemmasutra-Mini-2B-v1-Q3_K_M.gguf
    Q3_K_S: Gemmasutra-Mini-2B-v1-Q3_K_S.gguf
    Q4_0_4_4: Gemmasutra-Mini-2B-v1-Q4_0_4_4.gguf
    Q4_0_4_8: Gemmasutra-Mini-2B-v1-Q4_0_4_8.gguf
    Q4_0_8_8: Gemmasutra-Mini-2B-v1-Q4_0_8_8.gguf
    Q4_K: Gemmasutra-Mini-2B-v1-Q4_K.gguf
    Q4_K_M: Gemmasutra-Mini-2B-v1-Q4_K_M.gguf
    Q4_K_S: Gemmasutra-Mini-2B-v1-Q4_K_S.gguf
    Q5_K: Gemmasutra-Mini-2B-v1-Q5_K.gguf
    Q5_K_M: Gemmasutra-Mini-2B-v1-Q5_K_M.gguf
    Q5_K_S: Gemmasutra-Mini-2B-v1-Q5_K_S.gguf
    Q6_K: Gemmasutra-Mini-2B-v1-Q6_K.gguf
    Q8_0: Gemmasutra-Mini-2B-v1-Q8_0.gguf
    F16: Gemmasutra-Mini-2B-v1-f16.gguf
    F32: Gemmasutra-Mini-2B-v1-f32.gguf

- name: Gemmasutra Small 4B v1a
  repo: TheDrummer/Gemmasutra-Small-4B-v1-GGUF
  branch: main
  parameters: 4.0
  tags:
    Q2_K: Gemmasutra-Small-4B-v1a-Q2_K.gguf
    Q3_K_M: Gemmasutra-Small-4B-v1a-Q3_K_M.gguf
    Q4_K_M: Gemmasutra-Small-4B-v1a-Q4_K_M.gguf
    Q5_K_M: Gemmasutra-Small-4B-v1a-Q5_K_M.gguf
    Q6_K: Gemmasutra-Small-4B-v1a-Q6_K.gguf
    Q8_0: Gemmasutra-Small-4B-v1a-Q8_0.gguf

- name: Qwen2.5 1.5B Instruct
  repo: Qwen/Qwen2.5-1.5B-Instruct-GGUF
  branch: main
  parameters: 1.5
  tags:
    Q2_K: qwen2.5-1.5b-instruct-q2_k.gguf
    Q3_K_M: qwen2.5-1.5b-instruct-q3_k_m.gguf
    Q4_0: qwen2.5-1.5b-instruct-q4_0.gguf
    Q4_K_M: qwen2.5-1.5b-instruct-q4_k_m.gguf
    Q5_0: qwen2.5-1.5b-instruct-q5_0.gguf
    Q5_K_M: qwen2.5-1.5b-instruct-q5_k_m.gguf
    Q6_K: qwen2.5-1.5b-instruct-q6_k.gguf
    Q8_0: qwen2.5-1.5b-instruct-q8_0.gguf
    FP16: qwen2.5-1.5b-instruct-fp16.gguf

- name: Llama 3.2 1B Instruct
  repo: bartowski/Llama-3.2-1B-Instruct-GGUF
  branch: main
  parameters: 1.0
  tags:
    IQ3_M: Llama-3.2-1B-Instruct-IQ3_M.gguf
    IQ4_XS: Llama-3.2-1B-Instruct-IQ4_XS.gguf
    Q3_K_L: Llama-3.2-1B-Instruct-Q3_K_L.gguf
    Q3_K_XL: Llama-3.2-1B-Instruct-Q3_K_XL.gguf
    Q4_0: Llama-3.2-1B-Instruct-Q4_0.gguf
    Q4_0_4_4: Llama-3.2-1B-Instruct-Q4_0_4_4.gguf
    Q4_0_4_8: Llama-3.2-1B-Instruct-Q4_0_4_8.gguf
    Q4_0_8_8: Llama-3.2-1B-Instruct-Q4_0_8_8.gguf
    Q4_K_L: Llama-3.2-1B-Instruct-Q4_K_L.gguf
    Q4_K_M: Llama-3.2-1B-Instruct-Q4_K_M.gguf
    Q4_K_S: Llama-3.2-1B-Instruct-Q4_K_S.gguf
    Q5_K_L: Llama-3.2-1B-Instruct-Q5_K_L.gguf
    Q5_K_M: Llama-3.2-1B-Instruct-Q5_K_M.gguf
    Q5_K_S: Llama-3.2-1B-Instruct-Q5_K_S.gguf
    Q6_K: Llama-3.2-1B-Instruct-Q6_K.gguf
    Q6_K_L: Llama-3.2-1B-Instruct-Q6_K_L.gguf
    Q8_0: Llama-3.2-1B-Instruct-Q8_0.gguf
    F16: Llama-3.2-1B-Instruct-f16.gguf

- name: Llama 3.2 3B Instruct
  repo: bartowski/Llama-3.2-3B-Instruct-GGUF
  branch: main
  parameters: 3.0
  tags:
    IQ3_M: Llama-3.2-3B-Instruct-IQ3_M.gguf
    IQ4_XS: Llama-3.2-3B-Instruct-IQ4_XS.gguf
    Q3_K_L: Llama-3.2-3B-Instruct-Q3_K_L.gguf
    Q3_K_XL: Llama-3.2-3B-Instruct-Q3_K_XL.gguf
    Q4_0: Llama-3.2-3B-Instruct-Q4_0.gguf
    Q4_0_4_4: Llama-3.2-3B-Instruct-Q4_0_4_4.gguf
    Q4_0_4_8: Llama-3.2-3B-Instruct-Q4_0_4_8.gguf
    Q4_0_8_8: Llama-3.2-3B-Instruct-Q4_0_8_8.gguf
    Q4_K_L: Llama-3.2-3B-Instruct-Q4_K_L.gguf
    Q4_K_M: Llama-3.2-3B-Instruct-Q4_K_M.gguf
    Q4_K_S: Llama-3.2-3B-Instruct-Q4_K_S.gguf
    Q5_K_L: Llama-3.2-3B-Instruct-Q5_K_L.gguf
    Q5_K_M: Llama-3.2-3B-Instruct-Q5_K_M.gguf
    Q5_K_S: Llama-3.2-3B-Instruct-Q5_K_S.gguf
    Q6_K: Llama-3.2-3B-Instruct-Q6_K.gguf
    Q6_K_L: Llama-3.2-3B-Instruct-Q6_K_L.gguf
    Q8_0: Llama-3.2-3B-Instruct-Q8_0.gguf
    F16: Llama-3.2-3B-Instruct-f16.gguf

- name: Tesslate Tessa T1 3B
  repo: bartowski/Tesslate_Tessa-T1-3B-GGUF
  branch: main
  parameters: 3.0
  tags:
    IQ2_M: Tesslate_Tessa-T1-3B-IQ2_M.gguf
    IQ3_M: Tesslate_Tessa-T1-3B-IQ3_M.gguf
    IQ3_XS: Tesslate_Tessa-T1-3B-IQ3_XS.gguf
    IQ3_XXS: Tesslate_Tessa-T1-3B-IQ3_XXS.gguf
    IQ4_NL: Tesslate_Tessa-T1-3B-IQ4_NL.gguf
    IQ4_XS: Tesslate_Tessa-T1-3B-IQ4_XS.gguf
    Q2_K: Tesslate_Tessa-T1-3B-Q2_K.gguf
    Q2_K_L: Tesslate_Tessa-T1-3B-Q2_K_L.gguf
    Q3_K_L: Tesslate_Tessa-T1-3B-Q3_K_L.gguf
    Q3_K_M: Tesslate_Tessa-T1-3B-Q3_K_M.gguf
    Q3_K_S: Tesslate_Tessa-T1-3B-Q3_K_S.gguf
    Q3_K_XL: Tesslate_Tessa-T1-3B-Q3_K_XL.gguf
    Q4_0: Tesslate_Tessa-T1-3B-Q4_0.gguf
    Q4_1: Tesslate_Tessa-T1-3B-Q4_1.gguf
    Q4_K_L: Tesslate_Tessa-T1-3B-Q4_K_L.gguf
    Q4_K_M: Tesslate_Tessa-T1-3B-Q4_K_M.gguf
    Q4_K_S: Tesslate_Tessa-T1-3B-Q4_K_S.gguf
    Q5_K_L: Tesslate_Tessa-T1-3B-Q5_K_L.gguf
    Q5_K_M: Tesslate_Tessa-T1-3B-Q5_K_M.gguf
    Q5_K_S: Tesslate_Tessa-T1-3B-Q5_K_S.gguf
    Q6_K: Tesslate_Tessa-T1-3B-Q6_K.gguf
    Q6_K_L: Tesslate_Tessa-T1-3B-Q6_K_L.gguf
    Q8_0: Tesslate_Tessa-T1-3B-Q8_0.gguf
    BF16: Tesslate_Tessa-T1-3B-bf16.gguf

# NSFW model
#- name: NSFW 3B
#  repo: UnfilteredAI/NSFW-3B
#  branch: main
#  nsfw: true
#  parameters: 2.8
#  tags:
#    IQ4_XS: nsfw-3b-iq4_xs-imat.gguf
#    Q4_K_M: nsfw-3b-q4_k_m.gguf

# Emotionally aware NSFW model
#- name: NSFW Flash
#  repo: UnfilteredAI/NSFW-flash
#  branch: main
#  nsfw: true
#  parameters: 2.24
#  tags:
#    Q4_K_M: nsfw-flash-q4_k_m.gguf