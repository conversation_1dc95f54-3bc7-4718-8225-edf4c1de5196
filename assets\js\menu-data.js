// Menu Data - Baseado no cardápio original da Laricas
const menuData = {
    'pizzas-salgadas': [
        {
            id: 1,
            name: 'Pizza Margherita',
            description: '<PERSON><PERSON><PERSON> de tomate, mussarela especial, tomate fresco e manjericão',
            image: 'assets/images/pizza-margherita.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 25.90 },
                { size: 'M', price: 32.90 },
                { size: 'G', price: 42.90 },
                { size: 'GG', price: 52.90 }
            ],
            popular: true
        },
        {
            id: 2,
            name: 'Pizza Calabresa',
            description: '<PERSON><PERSON><PERSON> de tomate, mussarela, calabresa especial e cebola roxa',
            image: 'assets/images/pizza-calabresa.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 27.90 },
                { size: 'M', price: 35.90 },
                { size: 'G', price: 45.90 },
                { size: 'GG', price: 55.90 }
            ],
            popular: true
        },
        {
            id: 3,
            name: 'Pizza Portuguesa',
            description: '<PERSON><PERSON><PERSON> de tomate, mussarela, presunto, ovos, cebola, azeitona e orégano',
            image: 'assets/images/pizza-portuguesa.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 32.90 },
                { size: 'M', price: 42.90 },
                { size: 'G', price: 52.90 },
                { size: 'GG', price: 62.90 }
            ],
            popular: true
        },
        {
            id: 4,
            name: 'Pizza Frango com Catupiry',
            description: 'Molho de tomate, mussarela, frango desfiado temperado e catupiry',
            image: 'assets/images/pizza-frango-catupiry.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 29.90 },
                { size: 'M', price: 38.90 },
                { size: 'G', price: 48.90 },
                { size: 'GG', price: 58.90 }
            ]
        },
        {
            id: 5,
            name: 'Pizza Quatro Queijos',
            description: 'Molho de tomate, mussarela, provolone, parmesão e catupiry',
            image: 'assets/images/pizza-quatro-queijos.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 34.90 },
                { size: 'M', price: 44.90 },
                { size: 'G', price: 54.90 },
                { size: 'GG', price: 64.90 }
            ]
        },
        {
            id: 6,
            name: 'Pizza Pepperoni',
            description: 'Molho de tomate, mussarela e pepperoni importado',
            image: 'assets/images/pizza-pepperoni.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 31.90 },
                { size: 'M', price: 41.90 },
                { size: 'G', price: 51.90 },
                { size: 'GG', price: 61.90 }
            ]
        },
        {
            id: 7,
            name: 'Pizza Bacon',
            description: 'Molho de tomate, mussarela, bacon crocante e cebola caramelizada',
            image: 'assets/images/pizza-bacon.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 33.90 },
                { size: 'M', price: 43.90 },
                { size: 'G', price: 53.90 },
                { size: 'GG', price: 63.90 }
            ]
        },
        {
            id: 8,
            name: 'Pizza Vegetariana',
            description: 'Molho de tomate, mussarela, tomate, pimentão, cebola, azeitona e orégano',
            image: 'assets/images/pizza-vegetariana.jpg',
            category: 'pizzas-salgadas',
            sizes: [
                { size: 'P', price: 28.90 },
                { size: 'M', price: 37.90 },
                { size: 'G', price: 47.90 },
                { size: 'GG', price: 57.90 }
            ]
        }
    ],
    
    'pizzas-doces': [
        {
            id: 21,
            name: 'Pizza de Chocolate',
            description: 'Massa doce, chocolate ao leite derretido e granulado',
            image: 'assets/images/pizza-chocolate.jpg',
            category: 'pizzas-doces',
            sizes: [
                { size: 'P', price: 22.90 },
                { size: 'M', price: 29.90 },
                { size: 'G', price: 38.90 },
                { size: 'GG', price: 47.90 }
            ],
            popular: true
        },
        {
            id: 22,
            name: 'Pizza de Brigadeiro',
            description: 'Massa doce, brigadeiro cremoso e granulado colorido',
            image: 'assets/images/pizza-brigadeiro.jpg',
            category: 'pizzas-doces',
            sizes: [
                { size: 'P', price: 24.90 },
                { size: 'M', price: 32.90 },
                { size: 'G', price: 41.90 },
                { size: 'GG', price: 50.90 }
            ]
        },
        {
            id: 23,
            name: 'Pizza de Banana com Canela',
            description: 'Massa doce, banana fatiada, canela em pó e açúcar cristal',
            image: 'assets/images/pizza-banana-canela.jpg',
            category: 'pizzas-doces',
            sizes: [
                { size: 'P', price: 21.90 },
                { size: 'M', price: 28.90 },
                { size: 'G', price: 37.90 },
                { size: 'GG', price: 46.90 }
            ]
        },
        {
            id: 24,
            name: 'Pizza de Nutella',
            description: 'Massa doce, nutella derretida e morangos frescos',
            image: 'assets/images/pizza-nutella.jpg',
            category: 'pizzas-doces',
            sizes: [
                { size: 'P', price: 26.90 },
                { size: 'M', price: 35.90 },
                { size: 'G', price: 45.90 },
                { size: 'GG', price: 55.90 }
            ]
        }
    ],
    
    'esfihas': [
        {
            id: 41,
            name: 'Esfiha de Carne',
            description: 'Massa artesanal, carne temperada com cebola e temperos especiais',
            image: 'assets/images/esfiha-carne.jpg',
            category: 'esfihas',
            sizes: [
                { size: 'Unidade', price: 4.50 },
                { size: '6 unidades', price: 25.90 },
                { size: '12 unidades', price: 48.90 }
            ],
            popular: true
        },
        {
            id: 42,
            name: 'Esfiha de Frango',
            description: 'Massa artesanal, frango desfiado temperado com catupiry',
            image: 'assets/images/esfiha-frango.jpg',
            category: 'esfihas',
            sizes: [
                { size: 'Unidade', price: 4.90 },
                { size: '6 unidades', price: 27.90 },
                { size: '12 unidades', price: 52.90 }
            ]
        },
        {
            id: 43,
            name: 'Esfiha de Queijo',
            description: 'Massa artesanal, queijo mussarela derretido e orégano',
            image: 'assets/images/esfiha-queijo.jpg',
            category: 'esfihas',
            sizes: [
                { size: 'Unidade', price: 4.20 },
                { size: '6 unidades', price: 23.90 },
                { size: '12 unidades', price: 45.90 }
            ]
        },
        {
            id: 44,
            name: 'Esfiha de Calabresa',
            description: 'Massa artesanal, calabresa especial com cebola e pimentão',
            image: 'assets/images/esfiha-calabresa.jpg',
            category: 'esfihas',
            sizes: [
                { size: 'Unidade', price: 4.70 },
                { size: '6 unidades', price: 26.90 },
                { size: '12 unidades', price: 50.90 }
            ]
        }
    ],
    
    'bebidas': [
        {
            id: 61,
            name: 'Refrigerante Lata',
            description: 'Coca-Cola, Guaraná Antarctica, Fanta Laranja ou Sprite - 350ml',
            image: 'assets/images/refrigerante-lata.jpg',
            category: 'bebidas',
            sizes: [
                { size: '350ml', price: 4.50 }
            ]
        },
        {
            id: 62,
            name: 'Refrigerante 2L',
            description: 'Coca-Cola, Guaraná Antarctica, Fanta Laranja ou Sprite - 2 litros',
            image: 'assets/images/refrigerante-2l.jpg',
            category: 'bebidas',
            sizes: [
                { size: '2L', price: 8.90 }
            ],
            popular: true
        },
        {
            id: 63,
            name: 'Suco Natural',
            description: 'Laranja, Limão, Maracujá ou Acerola - 500ml',
            image: 'assets/images/suco-natural.jpg',
            category: 'bebidas',
            sizes: [
                { size: '500ml', price: 6.90 }
            ]
        },
        {
            id: 64,
            name: 'Água Mineral',
            description: 'Água mineral sem gás - 500ml',
            image: 'assets/images/agua-mineral.jpg',
            category: 'bebidas',
            sizes: [
                { size: '500ml', price: 2.50 }
            ]
        }
    ],
    
    'promocoes': [
        {
            id: 81,
            name: 'Combo Família',
            description: '2 Pizzas Grandes + 1 Refrigerante 2L + 6 Esfihas',
            image: 'assets/images/combo-familia.jpg',
            category: 'promocoes',
            sizes: [
                { size: 'Combo', price: 89.90 }
            ],
            popular: true,
            originalPrice: 110.00
        },
        {
            id: 82,
            name: 'Combo Casal',
            description: '1 Pizza Média + 1 Refrigerante 2L + 4 Esfihas',
            image: 'assets/images/combo-casal.jpg',
            category: 'promocoes',
            sizes: [
                { size: 'Combo', price: 54.90 }
            ],
            originalPrice: 68.00
        },
        {
            id: 83,
            name: 'Terça da Pizza',
            description: 'Todas as pizzas grandes com 30% de desconto',
            image: 'assets/images/promocao-terca.jpg',
            category: 'promocoes',
            sizes: [
                { size: 'Desconto', price: 0 }
            ],
            isPromotion: true,
            promotionText: '30% OFF'
        },
        {
            id: 84,
            name: 'Happy Hour Esfihas',
            description: '12 Esfihas variadas por apenas R$ 39,90',
            image: 'assets/images/happy-hour-esfihas.jpg',
            category: 'promocoes',
            sizes: [
                { size: '12 unidades', price: 39.90 }
            ],
            originalPrice: 55.00
        }
    ]
};

// Função para obter todos os itens de uma categoria
function getMenuItemsByCategory(category) {
    return menuData[category] || [];
}

// Função para obter um item específico por ID
function getMenuItemById(id) {
    for (const category in menuData) {
        const item = menuData[category].find(item => item.id === id);
        if (item) return item;
    }
    return null;
}

// Função para obter itens populares
function getPopularItems() {
    const popularItems = [];
    for (const category in menuData) {
        const categoryPopular = menuData[category].filter(item => item.popular);
        popularItems.push(...categoryPopular);
    }
    return popularItems;
}

// Função para buscar itens por nome
function searchMenuItems(query) {
    const results = [];
    const searchTerm = query.toLowerCase();
    
    for (const category in menuData) {
        const categoryResults = menuData[category].filter(item => 
            item.name.toLowerCase().includes(searchTerm) ||
            item.description.toLowerCase().includes(searchTerm)
        );
        results.push(...categoryResults);
    }
    
    return results;
}

// Função para obter todas as categorias
function getCategories() {
    return [
        { id: 'pizzas-salgadas', name: 'Pizzas Salgadas', icon: 'fas fa-pizza-slice' },
        { id: 'pizzas-doces', name: 'Pizzas Doces', icon: 'fas fa-cookie-bite' },
        { id: 'esfihas', name: 'Esfihas', icon: 'fas fa-bread-slice' },
        { id: 'bebidas', name: 'Bebidas', icon: 'fas fa-glass-water' },
        { id: 'promocoes', name: 'Promoções', icon: 'fas fa-tags' }
    ];
}

// Exportar para uso global
window.MenuData = {
    data: menuData,
    getMenuItemsByCategory,
    getMenuItemById,
    getPopularItems,
    searchMenuItems,
    getCategories
};
