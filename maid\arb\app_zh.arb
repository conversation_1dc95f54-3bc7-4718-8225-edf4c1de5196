{"@@locale": "zh", "friendlyName": "中文", "localeTitle": "区域", "defaultLocale": "默认区域", "loading": "加载中...", "loadModel": "加载模型", "downloadModel": "下载模型", "noModelSelected": "未选择模型", "noModelLoaded": "未加载模型", "localModels": "本地模型", "size": "大小", "parameters": "参数", "delete": "删除", "select": "选择", "import": "导入", "export": "导出", "edit": "编辑", "regenerate": "重新生成", "chatsTitle": "聊天", "newChat": "新聊天", "anErrorOccurred": "发生错误", "errorTitle": "错误", "key": "键", "value": "值", "ok": "确定", "proceed": "继续", "done": "完成", "close": "关闭", "save": "保存", "saveLabel": "保存 {label}", "selectTag": "选择标签", "next": "下一步", "previous": "上一步", "contentShared": "内容已分享", "setUserImage": "设置用户头像", "setAssistantImage": "设置助手头像", "loadUserImage": "加载用户头像", "loadAssistantImage": "加载助手头像", "userName": "用户名", "assistantName": "助手名称", "user": "用户", "assistant": "助手", "cancel": "取消", "aiEcosystem": "AI 生态系统", "llamaCpp": "Llama CPP", "llamaCppModel": "Llama CPP 模型", "remoteModel": "远程模型", "refreshRemoteModels": "刷新远程模型", "ollama": "Ollama", "searchLocalNetwork": "搜索本地网络", "localNetworkSearchTitle": "本地网络搜索", "localNetworkSearchContent": "此功能需要额外的权限来搜索本地网络中的 Ollama 实例。", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "模型参数", "addParameter": "添加参数", "removeParameter": "删除参数", "saveParameters": "保存参数", "importParameters": "导入参数", "exportParameters": "导出参数", "selectAiEcosystem": "选择 AI 生态系统", "selectRemoteModel": "选择远程模型", "selectThemeMode": "选择应用主题模式", "themeMode": "主题模式", "themeModeSystem": "系统", "themeModeLight": "浅色", "themeModeDark": "深色", "themeSeedColor": "主题种子颜色", "editMessage": "编辑消息", "settingsTitle": "设置", "aiSettings": "{aiType} 设置", "userSettings": "用户设置", "assistantSettings": "助手设置", "systemSettings": "系统设置", "systemPrompt": "系统提示词", "clearChats": "清空聊天", "resetSettings": "重置设置", "clearCache": "清除缓存", "aboutTitle": "关于", "aboutContent": "Maid 是一款跨平台、开源免费应用，可本地运行 llama.cpp 模型，并支持远程连接 Ollama、Mistral、Google Gemini 和 OpenAI 模型。Maid 兼容 Sillytavern 角色卡，让您能与喜爱的角色互动。Maid 可直接从 Hugging Face 下载精选模型。Maid 遵循 MIT 许可证分发，并不提供任何明示或暗示的担保。Maid 与 Hugging Face、Meta (Facebook)、MistralAI、OpenAI、Google、Microsoft 或其他提供兼容模型的公司无关。", "leadMaintainer": "主要维护者", "apiKey": "API 密钥", "baseUrl": "基础 URL", "scrollToRecent": "滚动到最近的消息", "clearPrompt": "清除提示词", "submitPrompt": "提交提示词", "stopPrompt": "停止提示词", "typeMessage": "输入消息...", "code": "代码", "copyLabel": "复制 {label}", "labelCopied": "{label} 已复制到剪贴板！", "debugTitle": "调试", "warning": "警告", "nsfwWarning": "该模型被有意设计用于生成NSFW（不适宜公开）的内容。这可能包括涉及酷刑、强奸、谋杀和/或性变态行为的露骨性或暴力内容。如果您对这些话题较为敏感，或这些话题的讨论违反了当地法律，请不要继续。", "login": "登录", "logout": "登出", "register": "注册", "email": "电子邮件", "password": "密码", "confirmPassword": "确认密码", "resetCode": "重置码", "resetCodeSent": "重置码已发送至您的电子邮件。", "sendResetCode": "发送重置码", "sendAgain": "再次发送", "required": "必填", "invalidEmail": "请输入有效的电子邮件", "invalidUserName": "必须为3-24个字符，字母数字或下划线", "invalidPasswordLength": "至少8个字符", "invalidPassword": "需包含大写字母、小写字母、数字和符号", "passwordNoMatch": "密码不匹配", "createAccount": "创建账户", "resetPassword": "重置密码", "backToLogin": "返回登录", "alreadyHaveAccount": "我已有账户", "success": "成功", "registrationSuccess": "注册成功", "resetSuccess": "您的密码已成功重置。", "emailVerify": "请检查您的电子邮件以进行验证。"}