<?php
/**
 * Arquivo de Configuração de Exemplo - Laricas Delivery Admin
 * 
 * Copie este arquivo para config.php e ajuste as configurações conforme necessário
 */

// Configurações do Banco de Dados
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'banco0508');
define('DB_USER', 'root');
define('DB_PASS', 'Ha31038866##');
define('DB_CHARSET', 'utf8mb4');

// Configurações de Segurança
define('SESSION_TIMEOUT', 120); // Timeout da sessão em minutos
define('MAX_LOGIN_ATTEMPTS', 5); // Máximo de tentativas de login
define('LOGIN_LOCKOUT_TIME', 15); // Tempo de bloqueio em minutos

// Configurações de Upload
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('UPLOAD_PATH', '../uploads/');

// Configurações de Email (para notificações)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'sua-senha-app');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Laricas Delivery');

// Configurações da Aplicação
define('APP_NAME', 'Laricas Delivery');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'https://laricasdelivery.com.br');
define('ADMIN_URL', 'https://laricasdelivery.com.br/admin');

// Configurações de Debug
define('DEBUG_MODE', false); // Ativar apenas em desenvolvimento
define('LOG_LEVEL', 'error'); // error, warning, info, debug

// Configurações de API Externa
define('VIACEP_API_URL', 'https://viacep.com.br/ws/');
define('WHATSAPP_API_URL', ''); // URL da API do WhatsApp Business (se houver)

// Configurações de Cache
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hora

// Configurações de Backup
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', '../backups/');
define('BACKUP_RETENTION_DAYS', 30);

// Configurações de Timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações de Localização
define('DEFAULT_LOCALE', 'pt_BR');
define('DEFAULT_CURRENCY', 'BRL');

// Configurações de Paginação
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// Configurações de Notificações
define('ENABLE_EMAIL_NOTIFICATIONS', true);
define('ENABLE_SMS_NOTIFICATIONS', false);
define('ENABLE_PUSH_NOTIFICATIONS', false);

// Configurações de Integração
define('GOOGLE_ANALYTICS_ID', ''); // ID do Google Analytics
define('FACEBOOK_PIXEL_ID', ''); // ID do Facebook Pixel
define('GOOGLE_MAPS_API_KEY', ''); // Chave da API do Google Maps

// Configurações de Delivery
define('DEFAULT_DELIVERY_FEE', 5.00);
define('FREE_DELIVERY_MINIMUM', 30.00);
define('MAX_DELIVERY_DISTANCE', 15); // em km
define('DEFAULT_DELIVERY_TIME', 45); // em minutos

// Configurações de Pagamento
define('ENABLE_PIX', true);
define('ENABLE_CREDIT_CARD', true);
define('ENABLE_CASH', true);
define('PIX_KEY', '<EMAIL>');

// Configurações de Horário de Funcionamento
define('DEFAULT_OPENING_HOURS', [
    'monday' => ['open' => '18:00', 'close' => '23:00', 'closed' => false],
    'tuesday' => ['open' => '18:00', 'close' => '23:00', 'closed' => false],
    'wednesday' => ['open' => '18:00', 'close' => '23:00', 'closed' => false],
    'thursday' => ['open' => '18:00', 'close' => '23:00', 'closed' => false],
    'friday' => ['open' => '18:00', 'close' => '00:00', 'closed' => false],
    'saturday' => ['open' => '18:00', 'close' => '00:00', 'closed' => false],
    'sunday' => ['open' => '18:00', 'close' => '23:00', 'closed' => false]
]);

// Configurações de Tema
define('THEME_PRIMARY_COLOR', '#d32f2f');
define('THEME_SECONDARY_COLOR', '#ff6f00');
define('THEME_ACCENT_COLOR', '#4caf50');

// Configurações de SEO
define('DEFAULT_META_TITLE', 'Laricas Delivery - A Melhor Pizza da Região');
define('DEFAULT_META_DESCRIPTION', 'Peça já sua pizza favorita! Delivery rápido e sabor incomparável.');
define('DEFAULT_META_KEYWORDS', 'pizza, delivery, Marília, esfiha, lanches');

// Configurações de Redes Sociais
define('FACEBOOK_URL', 'https://facebook.com/laricasdelivery');
define('INSTAGRAM_URL', 'https://instagram.com/laricasdelivery');
define('WHATSAPP_NUMBER', '5514999999999');

// Configurações de Logs
define('LOG_PATH', '../logs/');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('LOG_MAX_FILES', 10);

// Configurações de Performance
define('ENABLE_GZIP', true);
define('ENABLE_BROWSER_CACHE', true);
define('CACHE_CONTROL_MAX_AGE', 86400); // 24 horas

// Configurações de Segurança Avançada
define('ENABLE_CSRF_PROTECTION', true);
define('ENABLE_XSS_PROTECTION', true);
define('ENABLE_CONTENT_SECURITY_POLICY', true);
define('FORCE_HTTPS', false); // Ativar em produção

// Configurações de Rate Limiting
define('ENABLE_RATE_LIMITING', true);
define('RATE_LIMIT_REQUESTS', 100); // Requests por minuto
define('RATE_LIMIT_WINDOW', 60); // Janela em segundos

// Configurações de Manutenção
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'Sistema em manutenção. Voltamos em breve!');
define('MAINTENANCE_ALLOWED_IPS', ['127.0.0.1', '::1']);

// Configurações de Relatórios
define('ENABLE_REPORTS', true);
define('REPORT_CACHE_TIME', 1800); // 30 minutos
define('ENABLE_EXPORT', true);

// Configurações de Webhook
define('ENABLE_WEBHOOKS', false);
define('WEBHOOK_SECRET', ''); // Segredo para validação de webhooks

// Configurações de API
define('API_RATE_LIMIT', 1000); // Requests por hora
define('API_VERSION', 'v1');
define('ENABLE_API_DOCS', true);

// Configurações de Monitoramento
define('ENABLE_MONITORING', false);
define('MONITORING_ENDPOINT', '');
define('MONITORING_TOKEN', '');

// Configurações de CDN
define('ENABLE_CDN', false);
define('CDN_URL', '');
define('CDN_ASSETS_URL', '');

// Configurações de Internacionalização
define('ENABLE_I18N', false);
define('AVAILABLE_LANGUAGES', ['pt_BR', 'en_US', 'es_ES']);

// Configurações de Feature Flags
define('FEATURE_FLAGS', [
    'enable_loyalty_program' => false,
    'enable_reviews' => false,
    'enable_scheduling' => false,
    'enable_multiple_addresses' => false,
    'enable_order_tracking' => false,
    'enable_promotions' => true,
    'enable_coupons' => true,
    'enable_categories' => true
]);

// Configurações de Desenvolvimento
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_PATH . 'php_errors.log');
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_PATH . 'php_errors.log');
}

// Configurações de Headers de Segurança
if (ENABLE_XSS_PROTECTION) {
    header('X-XSS-Protection: 1; mode=block');
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('Referrer-Policy: strict-origin-when-cross-origin');
}

if (FORCE_HTTPS && !isset($_SERVER['HTTPS'])) {
    $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    header("Location: $redirectURL");
    exit();
}

// Configurações de Content Security Policy
if (ENABLE_CONTENT_SECURITY_POLICY) {
    $csp = "default-src 'self'; ";
    $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval' cdn.jsdelivr.net cdnjs.cloudflare.com code.jquery.com; ";
    $csp .= "style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com fonts.googleapis.com; ";
    $csp .= "font-src 'self' fonts.googleapis.com fonts.gstatic.com; ";
    $csp .= "img-src 'self' data: blob:; ";
    $csp .= "connect-src 'self' viacep.com.br; ";
    header("Content-Security-Policy: $csp");
}
?>
