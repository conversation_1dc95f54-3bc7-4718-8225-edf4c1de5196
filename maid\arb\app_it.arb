{"@@locale": "it", "friendlyName": "Italiano", "localeTitle": "<PERSON><PERSON>", "defaultLocale": "Lingua predefinita", "loading": "Caricamento...", "loadModel": "Carica <PERSON>lo", "downloadModel": "Scarica modello", "noModelSelected": "Nessun modello se<PERSON>o", "noModelLoaded": "<PERSON><PERSON><PERSON> modello caricato", "localModels": "Modelli locali", "size": "Dimensione", "parameters": "Parametri", "delete": "Elimina", "select": "Seleziona", "import": "Importa", "export": "Esporta", "edit": "Modifica", "regenerate": "<PERSON><PERSON><PERSON><PERSON>", "chatsTitle": "Cha<PERSON>", "newChat": "Nuova chat", "anErrorOccurred": "Si è verificato un errore", "errorTitle": "Errore", "key": "Chiave", "value": "Valore", "ok": "OK", "proceed": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "close": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "saveLabel": "Salva {label}", "selectTag": "Seleziona tag", "next": "Prossimo", "previous": "Precedente", "contentShared": "Contenuto condiviso", "setUserImage": "Imposta immagine utente", "setAssistantImage": "Imposta immagine assistente", "loadUserImage": "Carica immagine utente", "loadAssistantImage": "Carica immagine assistente", "userName": "Nome utente", "assistantName": "Nome assistente", "user": "Utente", "assistant": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "aiEcosystem": "Ecosistema IA", "llamaCpp": "Llama CPP", "llamaCppModel": "Modello Llama CPP", "remoteModel": "<PERSON><PERSON>", "refreshRemoteModels": "Ricarica modelli remoti", "ollama": "Ollama", "searchLocalNetwork": "Cerca nella rete locale", "localNetworkSearchTitle": "Cerca nella rete locale", "localNetworkSearchContent": "Questa funzionalità richiede permessi aggiuntivi per cercare le istanze di Ollama nella rete locale.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Parametri del modello", "addParameter": "Aggiungi parametro", "removeParameter": "Rimuovi parametro", "saveParameters": "Salva parametri", "importParameters": "Importa parametri", "exportParameters": "Esporta parametri", "selectAiEcosystem": "Seleziona ecosistema IA", "selectRemoteModel": "Seleziona modello remoto", "selectThemeMode": "Seleziona modalità tema dell'app", "themeMode": "Modalità tema", "themeModeSystem": "Sistema", "themeModeLight": "Chiaro", "themeModeDark": "<PERSON><PERSON>", "themeSeedColor": "'Colore di base del tema", "editMessage": "Modifica messaggio", "settingsTitle": "Impostazioni", "aiSettings": "Impostazioni {aiType}", "userSettings": "Impostazioni utente", "assistantSettings": "Impostazioni assistente", "systemSettings": "Impostazioni di sistema", "systemPrompt": "Prompt di sistema", "clearChats": "Svuota le chat", "resetSettings": "<PERSON><PERSON><PERSON><PERSON>ost<PERSON>", "clearCache": "Svuota la cache", "aboutTitle": "Informazioni", "aboutContent": "Maid è un'applicazione multipiattaforma gratuita e open source per interfacciarsi localmente con i modelli di llama.cpp, e da remoto con i modelli di Ollama, Mistral e OpenAI. Maid supporta le schede dei personaggi di sillytavern per permetterti di interagire con tutti i tuoi personaggi preferiti. Maid consente di scaricare, direttamente dall'app, una lista curata di modelli da huggingface. Maid è distribuito sotto la licenza MIT ed è fornito senza alcuna garanzia, esplicita o implicita. Maid non è affiliato con Huggingface, Meta (Facebook), MistralAi, OpenAI, Google, Microsoft o con qualsiasi altra azienda che fornisce un modello compatibile con questa applicazione.", "leadMaintainer": "Manutentore principale", "apiKey": "Chiave API", "baseUrl": "Base URL", "scrollToRecent": "Sc<PERSON>ri verso il recente", "clearPrompt": "<PERSON><PERSON><PERSON><PERSON> il prompt", "submitPrompt": "Invia il prompt", "stopPrompt": "Ferma il prompt", "typeMessage": "Scrivi un messaggio...", "code": "Codice", "copyLabel": "Copia {label}", "labelCopied": "{label} copiati negli appunti!", "debugTitle": "Debug", "warning": "Avviso", "nsfwWarning": "Questo modello è stato intenzionalmente progettato per generare contenuti NSFW. C<PERSON><PERSON> può includere contenuti sessuali espliciti o violenti che coinvolgono tortura, stupro, omicidio e/o comportamenti sessualmente devianti. Se sei sensibile a questi temi, o se la loro discussione viola le leggi locali, NON PROSEGUIRE.", "login": "Accedi", "logout": "<PERSON><PERSON><PERSON>", "register": "Registrati", "email": "Email", "password": "Password", "confirmPassword": "Conferma password", "resetCode": "Codice di reset", "resetCodeSent": "Un codice di reset è stato inviato alla tua email.", "sendResetCode": "Invia codice di reset", "sendAgain": "Invia di nuovo", "required": "Obbligatorio", "invalidEmail": "Inserisci un'email valida", "invalidUserName": "Deve essere tra 3 e 24 caratteri, alfan<PERSON><PERSON><PERSON> o underscore", "invalidPasswordLength": "Almeno 8 caratteri", "invalidPassword": "<PERSON>ludi ma<PERSON>, minuscole, numero e simbolo", "passwordNoMatch": "Le password non corrispondono", "createAccount": "Crea un account", "resetPassword": "Reimposta la password", "backToLogin": "Torna al login", "alreadyHaveAccount": "Ho già un account", "success": "Successo", "registrationSuccess": "Registrazione avvenuta con successo", "resetSuccess": "La tua password è stata reimpostata correttamente.", "emailVerify": "Controlla la tua email per la verifica."}