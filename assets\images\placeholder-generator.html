<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerador de Placeholders - Laricas Delivery</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .placeholder {
            width: 400px;
            height: 300px;
            background: linear-gradient(135deg, #d32f2f, #ff6f00);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .logo-placeholder {
            width: 200px;
            height: 80px;
            background: #d32f2f;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 0;
            border-radius: 8px;
        }
        .small-placeholder {
            width: 60px;
            height: 60px;
            background: #ff6f00;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
            border-radius: 50%;
        }
        h2 {
            color: #d32f2f;
            border-bottom: 2px solid #d32f2f;
            padding-bottom: 10px;
        }
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🍕 Gerador de Placeholders - Laricas Delivery</h1>
    
    <div class="instructions">
        <h3>📋 Instruções</h3>
        <p>Esta página gera placeholders visuais para as imagens do site. Para usar imagens reais:</p>
        <ol>
            <li>Substitua os placeholders por fotos reais dos produtos</li>
            <li>Mantenha as dimensões recomendadas (400x300px para produtos)</li>
            <li>Use formato JPG para fotos e PNG para logos</li>
            <li>Otimize as imagens para web (máximo 200KB por produto)</li>
        </ol>
    </div>

    <h2>🍕 Pizzas Salgadas</h2>
    <div class="grid">
        <div class="placeholder">Pizza<br>Margherita</div>
        <div class="placeholder">Pizza<br>Calabresa</div>
        <div class="placeholder">Pizza<br>Portuguesa</div>
        <div class="placeholder">Pizza Frango<br>com Catupiry</div>
        <div class="placeholder">Pizza<br>Quatro Queijos</div>
        <div class="placeholder">Pizza<br>Pepperoni</div>
        <div class="placeholder">Pizza<br>Bacon</div>
        <div class="placeholder">Pizza<br>Vegetariana</div>
    </div>

    <h2>🍰 Pizzas Doces</h2>
    <div class="grid">
        <div class="placeholder">Pizza de<br>Chocolate</div>
        <div class="placeholder">Pizza de<br>Brigadeiro</div>
        <div class="placeholder">Pizza Banana<br>com Canela</div>
        <div class="placeholder">Pizza de<br>Nutella</div>
    </div>

    <h2>🥟 Esfihas</h2>
    <div class="grid">
        <div class="placeholder">Esfiha de<br>Carne</div>
        <div class="placeholder">Esfiha de<br>Frango</div>
        <div class="placeholder">Esfiha de<br>Queijo</div>
        <div class="placeholder">Esfiha de<br>Calabresa</div>
    </div>

    <h2>🥤 Bebidas</h2>
    <div class="grid">
        <div class="placeholder">Refrigerante<br>Lata</div>
        <div class="placeholder">Refrigerante<br>2L</div>
        <div class="placeholder">Suco<br>Natural</div>
        <div class="placeholder">Água<br>Mineral</div>
    </div>

    <h2>🎉 Promoções</h2>
    <div class="grid">
        <div class="placeholder">Combo<br>Família</div>
        <div class="placeholder">Combo<br>Casal</div>
        <div class="placeholder">Terça da<br>Pizza</div>
        <div class="placeholder">Happy Hour<br>Esfihas</div>
    </div>

    <h2>🏢 Branding</h2>
    <div class="logo-placeholder">LARICAS DELIVERY</div>
    <div class="small-placeholder">LOGO</div>

    <h2>📸 Outras Imagens</h2>
    <div class="placeholder">Sobre Nós<br>(Pizza sendo preparada)</div>
    <div class="placeholder">Padrão de Fundo<br>(Hero Section)</div>

    <div class="instructions">
        <h3>🎨 Dicas de Fotografia</h3>
        <ul>
            <li><strong>Iluminação:</strong> Use luz natural ou softbox para evitar sombras duras</li>
            <li><strong>Ângulo:</strong> Fotografe de cima (45°) para mostrar melhor os ingredientes</li>
            <li><strong>Fundo:</strong> Use fundos neutros (madeira, mármore, tecido)</li>
            <li><strong>Composição:</strong> Centralize o produto e deixe espaço ao redor</li>
            <li><strong>Cores:</strong> Ajuste saturação e contraste para destacar os ingredientes</li>
        </ul>
    </div>

    <div class="instructions">
        <h3>🔧 Especificações Técnicas</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f0f0f0;">
                <th style="padding: 10px; border: 1px solid #ddd;">Tipo</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Dimensões</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Formato</th>
                <th style="padding: 10px; border: 1px solid #ddd;">Tamanho Max</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Produtos</td>
                <td style="padding: 10px; border: 1px solid #ddd;">400x300px</td>
                <td style="padding: 10px; border: 1px solid #ddd;">JPG</td>
                <td style="padding: 10px; border: 1px solid #ddd;">200KB</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Logo</td>
                <td style="padding: 10px; border: 1px solid #ddd;">200x80px</td>
                <td style="padding: 10px; border: 1px solid #ddd;">PNG</td>
                <td style="padding: 10px; border: 1px solid #ddd;">50KB</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Favicon</td>
                <td style="padding: 10px; border: 1px solid #ddd;">32x32px</td>
                <td style="padding: 10px; border: 1px solid #ddd;">ICO</td>
                <td style="padding: 10px; border: 1px solid #ddd;">10KB</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Hero/Banner</td>
                <td style="padding: 10px; border: 1px solid #ddd;">1920x1080px</td>
                <td style="padding: 10px; border: 1px solid #ddd;">JPG</td>
                <td style="padding: 10px; border: 1px solid #ddd;">500KB</td>
            </tr>
        </table>
    </div>

    <script>
        // Gerar placeholder.jpg programaticamente
        function generatePlaceholder() {
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');
            
            // Gradient background
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, '#d32f2f');
            gradient.addColorStop(1, '#ff6f00');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Imagem não', 200, 140);
            ctx.fillText('encontrada', 200, 170);
            
            // Convert to blob and download
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'placeholder.jpg';
                a.click();
                URL.revokeObjectURL(url);
            }, 'image/jpeg', 0.8);
        }
        
        // Add download button
        const button = document.createElement('button');
        button.textContent = '📥 Baixar placeholder.jpg';
        button.style.cssText = 'background: #d32f2f; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 20px 0;';
        button.onclick = generatePlaceholder;
        document.body.appendChild(button);
    </script>
</body>
</html>
