name: Build iOS

on:
  workflow_call:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/build-ios.yml'
      - 'ios/**'
      - 'lib/**'
      - '*.yaml'

jobs:
  build:
    runs-on: macos-14

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.0'

      - name: Install Apple Certificate
        uses: apple-actions/import-codesign-certs@v1
        with:
          p12-file-base64: ${{ secrets.APPLE_CERTIFICATE_P12 }}
          p12-password: ${{ secrets.APPLE_PASSWORD }}

      - name: Install the provisioning profile
        run: |
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo "${{ secrets.APPLE_IOS_PROVISION_PROFILE }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/Github_Actions.mobileprovision

      - name: Set up keychain for signing
        run: |
          security create-keychain -p "" build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "" build.keychain
          security set-keychain-settings -lut 3600 build.keychain
    
      - name: Verify Certificate Installation
        run: |
          security find-identity -v -p codesigning

      - name: Build iOS App
        run: |
          flutter pub get
          flutter build ipa -v --export-options-plist="ios/ExportOptions.plist"

      - name: Upload iOS Build
        uses: actions/upload-artifact@v4
        with:
          name: maid-ios
          path: build/ios/ipa/maid.ipa
