{"@@locale": "ru", "friendlyName": "Русский", "localeTitle": "Локаль", "defaultLocale": "Локаль по умолчанию", "loading": "Загрузка...", "loadModel": "Загрузить модель", "downloadModel": "Скачать модель", "noModelSelected": "Модель не выбрана", "noModelLoaded": "Модель не загружена", "localModels": "Локальные модели", "size": "Размер", "parameters": "Параметры", "delete": "Удалить", "select": "Выбрать", "import": "Импорт", "export": "Экспорт", "edit": "Редактировать", "regenerate": "Перегенерировать", "chatsTitle": "Чаты", "newChat": "Новый чат", "anErrorOccurred": "Произошла ошибка", "errorTitle": "Ошибка", "key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Значение", "ok": "OK", "proceed": "Продолжить", "done": "Готово", "close": "Закрыть", "save": "Сохранить", "saveLabel": "Сохранить {label}", "selectTag": "Выбрать тег", "next": "Далее", "previous": "Назад", "contentShared": "Контент отправлен", "setUserImage": "Установить изображение пользователя", "setAssistantImage": "Установить изображение ассистента", "loadUserImage": "Загрузить изображение пользователя", "loadAssistantImage": "Загрузить изображение ассистента", "userName": "Имя пользователя", "assistantName": "Имя ассистента", "user": "Пользователь", "assistant": "Ассистент", "cancel": "Отмена", "aiEcosystem": "Экосистема ИИ", "llamaCpp": "Llama CPP", "llamaCppModel": "Модель Llama CPP", "remoteModel": "Удаленная модель", "refreshRemoteModels": "Обновить удаленные модели", "ollama": "Ollama", "searchLocalNetwork": "Поиск в локальной сети", "localNetworkSearchTitle": "Поиск в локальной сети", "localNetworkSearchContent": "Эта функция требует дополнительных разрешений для поиска Ollama в вашей локальной сети.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Параметры модели", "addParameter": "Добавить параметр", "removeParameter": "Удалить параметр", "saveParameters": "Сохранить параметры", "importParameters": "Импорт пара<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportParameters": "Экспорт пара<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectAiEcosystem": "Выберите экосистему ИИ", "selectRemoteModel": "Выберите удаленную модель", "selectThemeMode": "Выберите тему приложения", "themeMode": "Тема", "themeModeSystem": "Системная", "themeModeLight": "Светлая", "themeModeDark": "Темная", "themeSeedColor": "Основной цвет темы", "editMessage": "Редактировать сообщение", "settingsTitle": "Настройки", "aiSettings": "Настройки {aiType}", "userSettings": "Настройки пользователя", "assistantSettings": "Настройки ассистента", "systemSettings": "Системные настройки", "systemPrompt": "Системный промпт", "clearChats": "Очистить чаты", "resetSettings": "Сбросить настройки", "clearCache": "Очистить кэш", "aboutTitle": "О программе", "aboutContent": "Maid — это кроссплатформенное бесплатное и открытое приложение для работы с моделями llama.cpp локально, а также с моделя<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Google Gemini и OpenAI удаленно. Maid поддерживает карточки персонажей Sillytavern, позволяя взаимодействовать со всеми вашими любимыми персонажами. В приложении можно загружать модели из Hugging Face. Maid распространяется под лицензией MIT и предоставляется без каких-либо гарантий, явных или подразумеваемых. Maid не связан с Hugging Face, Meta (Facebook), MistralAI, OpenAI, Google, Microsoft или другими компаниями, предоставляющими совместимые модели.", "leadMaintainer": "Ведущий разработчик", "apiKey": "API-к<PERSON><PERSON><PERSON>", "baseUrl": "Базовый URL", "scrollToRecent": "Прокрутить к последним сообщениям", "clearPrompt": "Очистить промпт", "submitPrompt": "Отправить промпт", "stopPrompt": "Остановить промпт", "typeMessage": "Введите сообщение...", "code": "<PERSON>од", "copyLabel": "Копировать {label}", "labelCopied": "{label} скопирован в буфер обмена!", "debugTitle": "Отладка", "warning": "Предупреждение", "nsfwWarning": "Эта модель была намеренно разработана для создания NSFW-контента. Это может включать откровенные сексуальные или жестокие сцены, включая пытки, изнасилование, убийство и/или сексуально девиантное поведение. Если вы чувствительны к таким темам или обсуждение таких тем нарушает местные законы, НЕ ПРОДОЛЖАЙТЕ.", "login": "Войти", "logout": "Выйти", "register": "Зарегистрироваться", "email": "Электронная почта", "password": "Пароль", "confirmPassword": "Подтвердите пароль", "resetCode": "Код сброса", "resetCodeSent": "Код сброса был отправлен на вашу электронную почту.", "sendResetCode": "Отправить код сброса", "sendAgain": "Отправить снова", "required": "Обязательно", "invalidEmail": "Пожалуйста, введите действующий электронный адрес", "invalidUserName": "Должно быть от 3 до 24 символов, буквы, цифры или нижнее подчеркивание", "invalidPasswordLength": "Минимум 8 символов", "invalidPassword": "Включайте большие и маленькие буквы, цифры и символы", "passwordNoMatch": "Пароли не совпадают", "createAccount": "Создать аккаунт", "resetPassword": "Сбросить пароль", "backToLogin": "Назад к входу", "alreadyHaveAccount": "У меня уже есть аккаунт", "success": "Успех", "registrationSuccess": "Регистрация прошла успешно", "resetSuccess": "Ваш пароль успешно сброшен.", "emailVerify": "Пожалуйста, проверьте свою электронную почту для подтверждения."}