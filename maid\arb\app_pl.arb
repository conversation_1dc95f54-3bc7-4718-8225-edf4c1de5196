{"@@locale": "pl", "friendlyName": "<PERSON><PERSON>", "localeTitle": "Język", "defaultLocale": "Domyślny język", "loading": "Ładowanie...", "loadModel": "<PERSON><PERSON><PERSON><PERSON> model", "downloadModel": "Pobierz model", "noModelSelected": "<PERSON><PERSON> w<PERSON> modelu", "noModelLoaded": "<PERSON><PERSON> w<PERSON>u", "localModels": "Modele lo<PERSON>ne", "size": "Rozmiar", "parameters": "Parametry", "delete": "Usuń", "select": "<PERSON><PERSON><PERSON><PERSON>", "import": "Import<PERSON>j", "export": "Eksportuj", "edit": "<PERSON><PERSON><PERSON><PERSON>", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatsTitle": "<PERSON><PERSON><PERSON>", "newChat": "<PERSON><PERSON> czat", "anErrorOccurred": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd", "errorTitle": "Błąd", "key": "<PERSON><PERSON>cz", "value": "<PERSON><PERSON><PERSON><PERSON>", "ok": "OK", "proceed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "close": "Zamknij", "save": "<PERSON><PERSON><PERSON><PERSON>", "saveLabel": "Zapisz {label}", "selectTag": "<PERSON><PERSON><PERSON><PERSON> tag", "next": "<PERSON><PERSON>", "previous": "Wstecz", "contentShared": "Udostępniono zawartość", "setUserImage": "Ustaw obraz użytkownika", "setAssistantImage": "Ustaw obraz asystenta", "loadUserImage": "Wczytaj obraz użytkownika", "loadAssistantImage": "Wczytaj obraz asystenta", "userName": "Nazwa użytkownika", "assistantName": "Nazwa <PERSON>", "user": "Użytkownik", "assistant": "Asystent", "cancel": "<PERSON><PERSON><PERSON>", "aiEcosystem": "Ekosystem AI", "llamaCpp": "Llama CPP", "llamaCppModel": "Model Llama CPP", "remoteModel": "<PERSON> <PERSON><PERSON><PERSON>", "refreshRemoteModels": "Odśwież modele zdalne", "ollama": "Ollama", "searchLocalNetwork": "Szukaj w sieci lokalnej", "localNetworkSearchTitle": "Wyszukiwanie w sieci lokalnej", "localNetworkSearchContent": "Ta funkcja wymaga dodatkowych uprawnień do wyszukiwania instancji Ollama w sieci lokalnej.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Parametry modelu", "addParameter": "<PERSON><PERSON><PERSON> parametr", "removeParameter": "Usuń parametr", "saveParameters": "<PERSON><PERSON><PERSON>z parametry", "importParameters": "Import<PERSON>j parametry", "exportParameters": "Eksportuj parametry", "selectAiEcosystem": "Wybierz ekosystem AI", "selectRemoteModel": "Wybierz model z<PERSON><PERSON>", "selectThemeMode": "<PERSON><PERSON><PERSON><PERSON> tryb motywu aplikacji", "themeMode": "<PERSON><PERSON> m<PERSON>", "themeModeSystem": "Systemowy", "themeModeLight": "<PERSON><PERSON><PERSON>", "themeModeDark": "Ciemny", "themeSeedColor": "<PERSON><PERSON> przewod<PERSON> motywu", "editMessage": "<PERSON><PERSON><PERSON><PERSON>", "settingsTitle": "Ustawienia", "aiSettings": "Ustawienia {aiType}", "userSettings": "Ustawienia użytkownika", "assistantSettings": "Ustawi<PERSON> asystenta", "systemSettings": "Ustawienia systemowe", "systemPrompt": "Podpowiedź systemowa", "clearChats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "resetSettings": "Zresetuj us<PERSON>wienia", "clearCache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pamięć podręczną", "aboutTitle": "Informacje", "aboutContent": "Maid jest darm<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wiel<PERSON><PERSON><PERSON><PERSON><PERSON> aplikacją umożliwiającą interakcję z lokalnymi modelami llama.cpp oraz zdalnie z modelami Ollama, Mistral i OpenAI. Maid obsługuje karty postac<PERSON>, umoż<PERSON>wiając interakcję ze wszystkimi ulubionymi postaciami. Aplikacja pozwala na pobieranie modeli bezpośrednio z Huggingface. Maid jest rozpowszechniany na licencji MIT i dostarczany bez żadnej gwarancji, wyrażonej lub domniemanej. Maid nie jest powiązany z Huggingface, Meta (Facebook), MistralAi, OpenAI, Google, Microsoft ani żadną inną firmą dostarczającą kompatybilny model.", "leadMaintainer": "Główny opiekun projektu", "apiKey": "Klucz API", "baseUrl": "Bazowy URL", "scrollToRecent": "Przewiń do najnowszych wiadomości", "clearPrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> podpowiedź", "submitPrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> podpowiedź", "stopPrompt": "Zatrzymaj podpowiedź", "typeMessage": "<PERSON><PERSON><PERSON> wiadom<PERSON>ść...", "code": "Kod", "copyLabel": "<PERSON><PERSON><PERSON><PERSON> {label}", "labelCopied": "{label} skopiowano do schowka!", "debugTitle": "Debugowanie", "warning": "Ostrzeżenie", "nsfwWarning": "Ten model z<PERSON><PERSON><PERSON> celowo zaprojektowany do generowania treści NSFW. <PERSON><PERSON><PERSON> to obejmować wyraźne treści seksualne lub brutalne, w tym tortury, gwa<PERSON>t, morderstwo i/lub zachowania seksualnie dewiacyjne. <PERSON><PERSON><PERSON> jesteś wrażliwy na takie tematy lub ich omawianie narusza lokalne prawo, NIE KONTYNUUJ.", "login": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "register": "Zarejestruj się", "email": "Email", "password": "<PERSON><PERSON><PERSON>", "confirmPassword": "Potwierd<PERSON> hasło", "resetCode": "<PERSON><PERSON>", "resetCodeSent": "Kod resetujący został wysłany na Twój email.", "sendResetCode": "Wyślij kod resetujący", "sendAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON>wn<PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON>", "invalidEmail": "Wprowadź prawidłowy email", "invalidUserName": "<PERSON><PERSON> mieć od 3 do 24 znaków, <PERSON><PERSON><PERSON><PERSON> litery, cy<PERSON><PERSON> lub podkreślenie", "invalidPasswordLength": "Minimum 8 znaków", "invalidPassword": "<PERSON><PERSON><PERSON><PERSON> wielkie, małe litery, cyfry i symbol", "passwordNoMatch": "Hasła nie pasują do siebie", "createAccount": "Utwórz konto", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "backToLogin": "Powrót do logowania", "alreadyHaveAccount": "<PERSON><PERSON> j<PERSON> konto", "success": "Sukces", "registrationSuccess": "Rejestracja zakończona sukcesem", "resetSuccess": "<PERSON><PERSON> hasło zostało pomyślnie zresetowane.", "emailVerify": "Sprawdź swój email, aby zweryfikować."}