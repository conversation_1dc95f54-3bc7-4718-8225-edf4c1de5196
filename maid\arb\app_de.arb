{"@@locale": "de", "friendlyName": "De<PERSON>ch", "localeTitle": "<PERSON><PERSON><PERSON>", "defaultLocale": "Standardsprache", "loading": "Laden...", "loadModel": "Modell laden", "downloadModel": "<PERSON><PERSON>", "noModelSelected": "<PERSON><PERSON> ausgewählt", "noModelLoaded": "<PERSON><PERSON> geladen", "localModels": "Lokale Modelle", "size": "Größe", "parameters": "Parameter", "delete": "Löschen", "select": "Auswählen", "import": "Importieren", "export": "Exportieren", "edit": "<PERSON><PERSON><PERSON>", "regenerate": "<PERSON><PERSON> gene<PERSON>", "chatsTitle": "Chats", "newChat": "<PERSON><PERSON><PERSON>", "anErrorOccurred": "Ein Fehler ist aufgetreten", "errorTitle": "<PERSON><PERSON>", "key": "Schlüssel", "value": "Wert", "ok": "OK", "proceed": "Fortfahren", "done": "<PERSON><PERSON><PERSON>", "close": "Schließen", "save": "Speichern", "saveLabel": "Speichern {label}", "selectTag": "Tag auswählen", "next": "<PERSON><PERSON>", "previous": "Zurück", "contentShared": "Inhalt geteilt", "setUserImage": "Benutzerbild festlegen", "setAssistantImage": "Assistentenbild festlegen", "loadUserImage": "Benutzerbild laden", "loadAssistantImage": "Assistentenbild laden", "userName": "<PERSON><PERSON><PERSON><PERSON>", "assistantName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON>", "assistant": "Assistent", "cancel": "Abbrechen", "aiEcosystem": "KI-Ökosystem", "llamaCpp": "Llama CPP", "llamaCppModel": "Llama CPP Modell", "remoteModel": "Remote-Modell", "refreshRemoteModels": "Remote-Modelle aktualisieren", "ollama": "Ollama", "searchLocalNetwork": "Lokales Netzwerk durchsuchen", "localNetworkSearchTitle": "Suche im lokalen Netzwerk", "localNetworkSearchContent": "Diese Funktion benötigt zusätzliche Berechtigungen, um Ihr lokales Netzwerk nach Ollama-Instanzen zu durchsuchen.", "openAI": "OpenAI", "mistral": "<PERSON><PERSON><PERSON>", "anthropic": "Anthropic", "gemini": "Gemini", "modelParameters": "Modellparameter", "addParameter": "Parameter hinzufügen", "removeParameter": "Parameter entfernen", "saveParameters": "Parameter speichern", "importParameters": "Parameter importieren", "exportParameters": "Parameter exportieren", "selectAiEcosystem": "KI-Ökosystem auswählen", "selectRemoteModel": "Remote-Modell auswählen", "selectThemeMode": "App-Designmodus au<PERSON>wählen", "themeMode": "Designmodus", "themeModeSystem": "System", "themeModeLight": "Hell", "themeModeDark": "<PERSON><PERSON><PERSON>", "themeSeedColor": "Design-<PERSON><PERSON><PERSON>", "editMessage": "Nachricht bearbeiten", "settingsTitle": "Einstellungen", "aiSettings": "{aiType} Einstellungen", "userSettings": "Benutzereinstellungen", "assistantSettings": "Assistenteneinstellungen", "systemSettings": "Systemeinstellungen", "systemPrompt": "System-Prompt", "clearChats": "Chats löschen", "resetSettings": "Einstellungen zurücksetzen", "clearCache": "<PERSON><PERSON> le<PERSON>n", "aboutTitle": "<PERSON><PERSON>", "aboutContent": "Maid ist eine plattformübergreifende, kostenlose und Open-Source-Anwendung zur Nutzung von llama.cpp-Modellen lokal und für die Fernverwendung mit Ollama-, Mistral- und OpenAI-Modellen. Maid unterstützt SillyTavern-<PERSON><PERSON><PERSON><PERSON><PERSON>, sodass Si<PERSON> mit Ihren Lieblingscharakteren interagieren können. Maid ermöglicht den direkten Download einer kuratierten Liste von Modellen aus Hugging Face. Maid wird unter der MIT-Lizenz vertrieben und ohne jegliche Gewährleistung bereitgestellt, weder ausdrücklich noch impliziert. Maid ist nicht mit Hugging Face, Meta (Facebook), MistralAI, OpenAI, Google, Microsoft oder anderen Unternehmen verbunden, die mit dieser Anwendung kompatible Modelle anbieten.", "leadMaintainer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apiKey": "API-Schlüssel", "baseUrl": "Basis-URL", "scrollToRecent": "Zuletzt verwendet", "clearPrompt": "Prompt löschen", "submitPrompt": "Prompt senden", "stopPrompt": "Prompt stoppen", "typeMessage": "Nachricht eingeben...", "code": "Code", "copyLabel": "{label} kopieren", "labelCopied": "{label} in die Zwischenablage kopiert!", "debugTitle": "Debugging", "warning": "Achtung", "nsfwWarning": "Dieses Modell wurde absichtlich so konzipiert, dass es nicht jugendfreier (NSFW) Inhalte erzeugt. Dazu können explizite sexuelle oder gewalttätige Inhalte gehören, e<PERSON><PERSON><PERSON><PERSON><PERSON>olter, Vergewaltigung, <PERSON>rd und/oder sexuell abweichendes Verhalten. Wenn Sie gegenüber solchen Themen sensibel sind oder deren Diskussion gegen lokale Gesetze verstößt, BRECHEN SIE JETZT AB.", "login": "Anmelden", "logout": "Abmelden", "register": "Registrieren", "email": "E-Mail", "password": "Passwort", "confirmPassword": "Passwort bestätigen", "resetCode": "Zurücksetzungscode", "resetCodeSent": "Ein Zurücksetzungscode wurde an Ihre E-Mail gesendet.", "sendResetCode": "Zurücksetzungscode senden", "sendAgain": "<PERSON><PERSON><PERSON> senden", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "invalidUserName": "3–24 <PERSON><PERSON><PERSON>, alphanumerisch oder Unterstrich", "invalidPasswordLength": "Mindestens 8 Zeichen", "invalidPassword": "<PERSON><PERSON><PERSON><PERSON>, Klein<PERSON>chs<PERSON>ben, Zahl und Symbol erforderlich", "passwordNoMatch": "Passwörter stimmen nicht überein", "createAccount": "<PERSON><PERSON> er<PERSON>", "resetPassword": "Passwort zurücksetzen", "backToLogin": "Zurück zur Anmeldung", "alreadyHaveAccount": "Ich habe bereits ein <PERSON>nto", "success": "Erfolg", "registrationSuccess": "Registrierung erfolgreich", "resetSuccess": "Ihr Passwort wurde erfolgreich zurückgesetzt.", "emailVerify": "Bitte überprüfen Sie Ihre E-Mails zur Verifizierung."}