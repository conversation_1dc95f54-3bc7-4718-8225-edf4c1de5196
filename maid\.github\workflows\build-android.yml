name: Build Android

on:
  workflow_call:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/build-android.yml'
      - 'android/**'
      - 'lib/**'
      - '*.yaml'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'adopt'

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y cmake

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.0'

      - name: Decode Keystore File
        env:
          KEYSTORE: ${{ secrets.ANDROID_KEYSTORE }}
        run: echo "$KEYSTORE" | base64 --decode > android/app/key.jks

      - name: Create key.properties
        run: |
          echo "storeFile=key.jks" > android/key.properties
          echo "storePassword=${{ secrets.ANDROID_STORE_PASSWORD }}" >> android/key.properties
          echo "keyPassword=${{ secrets.ANDROID_KEY_PASSWORD }}" >> android/key.properties
          echo "releasePassword=${{ secrets.ANDROID_KEY_PASSWORD }}" >> android/key.properties
          echo "keyAlias=${{ secrets.ANDROID_KEY_ALIAS }}" >> android/key.properties
          echo "releaseAlias=${{ secrets.ANDROID_KEY_ALIAS }}" >> android/key.properties

      - name: Setup Flutter
        run: |
          flutter config --no-analytics
          flutter pub get

      - name: Build APK
        run: |
          flutter build apk -v

      - name: Rename APK
        run: |
          mkdir -p release
          mv build/app/outputs/flutter-apk/app-release.apk release/maid-android-universal.apk
          mv build/app/outputs/flutter-apk/app-arm64-v8a-release.apk release/maid-android-arm64-v8a.apk
          mv build/app/outputs/flutter-apk/app-x86_64-release.apk release/maid-android-x86_64.apk

      - name: Build appbundle
        run: |
          flutter build appbundle --dart-define=WORKSAFE=true

      - name: Rename AAB
        run: mv build/app/outputs/bundle/release/app-release.aab release/maid-android-bundle.aab

      - name: Upload Android Builds
        uses: actions/upload-artifact@v4
        with:
          name: maid-android
          path: release