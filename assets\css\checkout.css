/* Checkout Page Styles */

/* Header Adjustments */
.checkout-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.progress-step {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-light);
    font-size: var(--font-size-sm);
    position: relative;
}

.progress-step.active {
    color: var(--primary-color);
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 2px;
    background-color: var(--border-color);
}

.progress-step.active:not(:last-child)::after {
    background-color: var(--primary-color);
}

.progress-step i {
    font-size: var(--font-size-lg);
}

/* Main Content */
.checkout-main {
    padding-top: 100px;
    padding-bottom: var(--spacing-3xl);
    min-height: calc(100vh - 200px);
    background-color: var(--background-light);
}

.checkout-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-3xl);
    align-items: flex-start;
}

/* Order Summary */
.order-summary {
    background-color: var(--background-white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 120px;
}

.order-summary h2 {
    color: var(--text-dark);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.order-summary h2 i {
    color: var(--primary-color);
}

.order-items {
    margin-bottom: var(--spacing-xl);
}

.checkout-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.checkout-item:last-child {
    border-bottom: none;
}

.checkout-item-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    overflow: hidden;
    flex-shrink: 0;
}

.checkout-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.checkout-item-details {
    flex: 1;
}

.checkout-item-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-xs);
}

.checkout-item-size {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.checkout-item-quantity {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.checkout-item-price {
    font-weight: 600;
    color: var(--primary-color);
    text-align: right;
}

.order-totals {
    border-top: 2px solid var(--border-color);
    padding-top: var(--spacing-lg);
}

.total-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    color: var(--text-dark);
}

.total-final {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

/* Checkout Form */
.checkout-form {
    background-color: var(--background-white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
}

.form-section {
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h3 {
    color: var(--text-dark);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-section h3 i {
    color: var(--primary-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group input.error,
.form-group textarea.error {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Payment Options */
.payment-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.payment-option {
    position: relative;
}

.payment-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.payment-option label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
    background-color: var(--background-white);
}

.payment-option label:hover {
    border-color: var(--primary-color);
    background-color: rgba(211, 47, 47, 0.05);
}

.payment-option input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(211, 47, 47, 0.1);
    color: var(--primary-color);
}

.payment-option label i {
    font-size: var(--font-size-2xl);
}

.payment-option label span {
    font-weight: 500;
}

.payment-details {
    display: none;
    padding: var(--spacing-lg);
    background-color: var(--background-light);
    border-radius: var(--radius-md);
    margin-top: var(--spacing-md);
}

.payment-details.active {
    display: block;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: space-between;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-xl);
    border-top: 2px solid var(--border-color);
}

.form-actions .btn {
    flex: 1;
    max-width: 200px;
}

/* Empty Cart State */
.empty-cart {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-light);
}

.empty-cart i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    color: var(--border-color);
}

.empty-cart h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

.empty-cart p {
    margin-bottom: var(--spacing-xl);
}

/* Loading States */
.checkout-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl);
    color: var(--text-light);
}

.checkout-loading i {
    font-size: var(--font-size-2xl);
    margin-right: var(--spacing-md);
    animation: spin 1s linear infinite;
}

/* Error Messages */
.field-error {
    color: #f44336;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.field-error i {
    font-size: var(--font-size-sm);
}

/* Success State */
.checkout-success {
    text-align: center;
    padding: var(--spacing-3xl);
    background-color: var(--background-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
}

.checkout-success i {
    font-size: var(--font-size-4xl);
    color: var(--accent-color);
    margin-bottom: var(--spacing-lg);
}

.checkout-success h2 {
    color: var(--text-dark);
    margin-bottom: var(--spacing-md);
}

.checkout-success p {
    margin-bottom: var(--spacing-xl);
    color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .checkout-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .order-summary {
        position: static;
        order: 2;
    }
    
    .checkout-form {
        order: 1;
    }
}

@media (max-width: 768px) {
    .checkout-progress {
        display: none;
    }
    
    .checkout-main {
        padding-top: 80px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .payment-options {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        max-width: none;
    }
}

@media (max-width: 480px) {
    .checkout-content {
        gap: var(--spacing-lg);
    }
    
    .order-summary,
    .checkout-form {
        padding: var(--spacing-lg);
    }
    
    .checkout-item {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .checkout-item-image {
        align-self: center;
    }
    
    .checkout-item-price {
        text-align: left;
    }
}
