<?php
/**
 * Sistema de Autenticação - Laricas Delivery Admin
 */

require_once __DIR__ . '/../config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
        $this->startSession();
    }
    
    private function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Fazer login do usuário
     */
    public function login($username, $password) {
        try {
            $sql = "SELECT * FROM admins WHERE (username = :username OR email = :username) AND status = 'active'";
            $user = $this->db->fetch($sql, ['username' => $username]);
            
            if ($user && password_verify($password, $user['password'])) {
                // Atualizar último login
                $this->db->update('admins', 
                    ['last_login' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $user['id']]
                );
                
                // Criar sessão
                $_SESSION['admin_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_name'] = $user['full_name'];
                $_SESSION['admin_role'] = $user['role'];
                $_SESSION['admin_email'] = $user['email'];
                $_SESSION['login_time'] = time();
                
                // Log da atividade
                $this->logActivity('login', null, null, null, ['username' => $username]);
                
                return [
                    'success' => true,
                    'message' => 'Login realizado com sucesso',
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'name' => $user['full_name'],
                        'role' => $user['role'],
                        'email' => $user['email']
                    ]
                ];
            } else {
                // Log da tentativa de login falhada
                $this->logActivity('login_failed', null, null, null, ['username' => $username]);
                
                return [
                    'success' => false,
                    'message' => 'Usuário ou senha incorretos'
                ];
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Erro interno do servidor'
            ];
        }
    }
    
    /**
     * Fazer logout do usuário
     */
    public function logout() {
        if ($this->isLoggedIn()) {
            $this->logActivity('logout');
        }
        
        session_destroy();
        return [
            'success' => true,
            'message' => 'Logout realizado com sucesso'
        ];
    }
    
    /**
     * Verificar se o usuário está logado
     */
    public function isLoggedIn() {
        return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
    }
    
    /**
     * Obter dados do usuário logado
     */
    public function getUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['admin_id'],
            'username' => $_SESSION['admin_username'],
            'name' => $_SESSION['admin_name'],
            'role' => $_SESSION['admin_role'],
            'email' => $_SESSION['admin_email'],
            'login_time' => $_SESSION['login_time']
        ];
    }
    
    /**
     * Verificar permissões do usuário
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $role = $_SESSION['admin_role'];
        
        // Admin tem todas as permissões
        if ($role === 'admin') {
            return true;
        }
        
        // Definir permissões por role
        $permissions = [
            'manager' => [
                'view_dashboard',
                'view_orders',
                'edit_orders',
                'view_products',
                'edit_products',
                'view_customers',
                'view_reports'
            ]
        ];
        
        return isset($permissions[$role]) && in_array($permission, $permissions[$role]);
    }
    
    /**
     * Alterar senha do usuário
     */
    public function changePassword($currentPassword, $newPassword) {
        if (!$this->isLoggedIn()) {
            return ['success' => false, 'message' => 'Usuário não autenticado'];
        }
        
        try {
            $userId = $_SESSION['admin_id'];
            $user = $this->db->fetch("SELECT password FROM admins WHERE id = :id", ['id' => $userId]);
            
            if (!$user || !password_verify($currentPassword, $user['password'])) {
                return ['success' => false, 'message' => 'Senha atual incorreta'];
            }
            
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $this->db->update('admins', 
                ['password' => $hashedPassword], 
                'id = :id', 
                ['id' => $userId]
            );
            
            $this->logActivity('password_changed');
            
            return ['success' => true, 'message' => 'Senha alterada com sucesso'];
            
        } catch (Exception $e) {
            error_log("Change password error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Erro interno do servidor'];
        }
    }
    
    /**
     * Criar novo usuário admin
     */
    public function createUser($data) {
        if (!$this->hasPermission('manage_users')) {
            return ['success' => false, 'message' => 'Sem permissão para criar usuários'];
        }
        
        try {
            // Verificar se username ou email já existem
            $existing = $this->db->fetch(
                "SELECT id FROM admins WHERE username = :username OR email = :email",
                ['username' => $data['username'], 'email' => $data['email']]
            );
            
            if ($existing) {
                return ['success' => false, 'message' => 'Usuário ou email já existem'];
            }
            
            $userData = [
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'full_name' => $data['full_name'],
                'role' => $data['role'] ?? 'manager',
                'status' => $data['status'] ?? 'active'
            ];
            
            $userId = $this->db->insert('admins', $userData);
            
            $this->logActivity('user_created', 'admins', $userId, null, $userData);
            
            return [
                'success' => true,
                'message' => 'Usuário criado com sucesso',
                'user_id' => $userId
            ];
            
        } catch (Exception $e) {
            error_log("Create user error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Erro ao criar usuário'];
        }
    }
    
    /**
     * Registrar atividade do usuário
     */
    public function logActivity($action, $table = null, $recordId = null, $oldValues = null, $newValues = null) {
        try {
            $data = [
                'admin_id' => $this->isLoggedIn() ? $_SESSION['admin_id'] : null,
                'action' => $action,
                'table_name' => $table,
                'record_id' => $recordId,
                'old_values' => $oldValues ? json_encode($oldValues) : null,
                'new_values' => $newValues ? json_encode($newValues) : null,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ];
            
            $this->db->insert('activity_logs', $data);
        } catch (Exception $e) {
            error_log("Log activity error: " . $e->getMessage());
        }
    }
    
    /**
     * Verificar se a sessão expirou
     */
    public function checkSessionTimeout($timeoutMinutes = 120) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $loginTime = $_SESSION['login_time'] ?? 0;
        $currentTime = time();
        $timeoutSeconds = $timeoutMinutes * 60;
        
        if (($currentTime - $loginTime) > $timeoutSeconds) {
            $this->logout();
            return true;
        }
        
        return false;
    }
    
    /**
     * Middleware para verificar autenticação
     */
    public function requireAuth($redirectUrl = 'login.php') {
        if (!$this->isLoggedIn() || $this->checkSessionTimeout()) {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                // Requisição AJAX
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Sessão expirada']);
                exit;
            } else {
                // Requisição normal
                header("Location: {$redirectUrl}");
                exit;
            }
        }
    }
    
    /**
     * Middleware para verificar permissões
     */
    public function requirePermission($permission, $redirectUrl = 'dashboard.php') {
        $this->requireAuth();
        
        if (!$this->hasPermission($permission)) {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Sem permissão']);
                exit;
            } else {
                header("Location: {$redirectUrl}?error=no_permission");
                exit;
            }
        }
    }
}

// Instância global do sistema de autenticação
$auth = new Auth();

// Funções auxiliares
function requireAuth($redirectUrl = 'login.php') {
    global $auth;
    $auth->requireAuth($redirectUrl);
}

function requirePermission($permission, $redirectUrl = 'dashboard.php') {
    global $auth;
    $auth->requirePermission($permission, $redirectUrl);
}

function isLoggedIn() {
    global $auth;
    return $auth->isLoggedIn();
}

function getUser() {
    global $auth;
    return $auth->getUser();
}

function hasPermission($permission) {
    global $auth;
    return $auth->hasPermission($permission);
}

function logActivity($action, $table = null, $recordId = null, $oldValues = null, $newValues = null) {
    global $auth;
    $auth->logActivity($action, $table, $recordId, $oldValues, $newValues);
}
?>
