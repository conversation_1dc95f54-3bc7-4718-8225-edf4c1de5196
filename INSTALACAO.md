# 🚀 Guia de Instalação - Laricas Delivery

Este guia irá ajudá-lo a configurar e personalizar o site da Laricas Delivery passo a passo.

## 📋 Pré-requisitos

- Servidor web (Apache, Nginx, ou similar)
- Navegador web moderno
- Editor de código (VS Code, Sublime Text, etc.)
- Acesso FTP/SFTP para upload (se hospedando remotamente)

## 🔧 Instalação Básica

### 1. Download e Extração
1. Faça o download de todos os arquivos do projeto
2. Extraia para a pasta do seu servidor web (ex: `htdocs`, `www`, `public_html`)
3. Certifique-se de que a estrutura de pastas está correta:

```
laricas-delivery/
├── index.html
├── checkout.html
├── assets/
│   ├── css/
│   ├── js/
│   └── images/
└── README.md
```

### 2. Teste Inicial
1. Abra o navegador e acesse `http://localhost/laricas-delivery/`
2. Verifique se a página carrega corretamente
3. Teste a navegação entre as seções
4. Verifique se o carrinho funciona

## ⚙️ Configuração Personalizada

### 1. Informações da Pizzaria

Edite o arquivo `assets/js/config.js`:

```javascript
business: {
    name: 'SUA PIZZARIA',
    tagline: 'Seu slogan aqui',
    phone: '5511999999999', // Formato: país + DDD + número
    phoneDisplay: '(11) 99999-9999',
    email: '<EMAIL>',
    // ... outras configurações
}
```

### 2. Horários de Funcionamento

No mesmo arquivo `config.js`:

```javascript
hours: {
    weekdays: 'Seg-Qui: 18h às 23h',
    weekend: 'Sex-Sáb: 18h à 00h',
    sunday: 'Domingo: 18h às 23h',
    display: 'Seg-Qui: 18h-23h | Sex-Sáb: 18h-00h | Dom: 18h-23h'
}
```

### 3. Configurações de Delivery

```javascript
delivery: {
    freeDeliveryMinimum: 30.00, // Valor mínimo para frete grátis
    deliveryFee: 5.00,          // Taxa de entrega padrão
    deliveryAreas: [            // Áreas de entrega
        'Centro',
        'Bairro 1',
        'Bairro 2'
    ]
}
```

## 🍕 Configuração do Cardápio

### 1. Editando Produtos

Abra `assets/js/menu-data.js` e edite os produtos:

```javascript
{
    id: 1,
    name: 'Nome da Pizza',
    description: 'Descrição detalhada dos ingredientes',
    image: 'assets/images/pizza-nome.jpg',
    category: 'pizzas-salgadas',
    sizes: [
        { size: 'P', price: 25.90 },
        { size: 'M', price: 32.90 },
        { size: 'G', price: 42.90 }
    ],
    popular: true // Para destacar como popular
}
```

### 2. Adicionando Novos Produtos

1. Copie um produto existente
2. Altere o `id` para um número único
3. Atualize nome, descrição, imagem e preços
4. Adicione a imagem correspondente na pasta `assets/images/`

### 3. Removendo Produtos

1. Localize o produto no array da categoria
2. Remova o objeto completo
3. Remova a imagem correspondente (opcional)

## 🖼️ Configuração de Imagens

### 1. Imagens Necessárias

Consulte `assets/images/README.md` para a lista completa de imagens necessárias.

### 2. Adicionando Imagens

1. Redimensione as imagens para 400x300px (produtos)
2. Otimize para web (máximo 200KB por imagem)
3. Salve com nomes correspondentes ao código:
   - `pizza-margherita.jpg`
   - `pizza-calabresa.jpg`
   - etc.

### 3. Logo da Pizzaria

1. Crie um logo em PNG (200x80px)
2. Salve como `assets/images/logo-laricas.png`
3. Crie um favicon 32x32px como `assets/images/favicon.ico`

## 🎨 Personalização Visual

### 1. Cores do Site

Edite as cores em `assets/js/config.js`:

```javascript
theme: {
    primary: '#d32f2f',      // Cor principal (vermelho)
    secondary: '#ff6f00',    // Cor secundária (laranja)
    accent: '#4caf50',       // Cor de destaque (verde)
    // ... outras cores
}
```

### 2. Fontes

Para alterar a fonte, edite `assets/css/style.css`:

```css
@import url('https://fonts.googleapis.com/css2?family=SuaFonte:wght@300;400;500;600;700&display=swap');

:root {
    --font-family: 'SuaFonte', sans-serif;
}
```

## 📱 WhatsApp Integration

### 1. Configuração do Número

No arquivo `config.js`, configure o número do WhatsApp:

```javascript
business: {
    phone: '5514999999999', // Formato: 55 + DDD + número
}
```

### 2. Mensagens Personalizadas

Personalize as mensagens automáticas:

```javascript
messages: {
    whatsappGreeting: 'Olá! Gostaria de fazer um pedido na Sua Pizzaria.',
    whatsappOrderHeader: '*🍕 PEDIDO SUA PIZZARIA*',
}
```

## 🌐 Hospedagem e Deploy

### 1. Hospedagem Gratuita

**GitHub Pages:**
1. Crie um repositório no GitHub
2. Faça upload dos arquivos
3. Ative GitHub Pages nas configurações
4. Acesse via `https://seuusuario.github.io/nome-repositorio`

**Netlify:**
1. Crie conta no Netlify
2. Arraste a pasta do projeto para o painel
3. Site estará disponível em poucos minutos

### 2. Hospedagem Paga

**Servidor Compartilhado:**
1. Contrate hospedagem com suporte a HTML/CSS/JS
2. Faça upload via FTP/cPanel
3. Configure domínio personalizado

### 3. Configurações do Servidor

Adicione ao `.htaccess` (Apache):

```apache
# Compressão GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache de arquivos estáticos
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

## 🔍 SEO e Analytics

### 1. Google Analytics

No arquivo `config.js`:

```javascript
seo: {
    googleAnalyticsId: 'GA_MEASUREMENT_ID',
}
```

### 2. Meta Tags

Edite as meta tags nos arquivos HTML:

```html
<title>Sua Pizzaria - Delivery</title>
<meta name="description" content="Descrição da sua pizzaria">
<meta name="keywords" content="pizza, delivery, sua cidade">
```

### 3. Schema.org

Configure os dados estruturados em `config.js`:

```javascript
schema: {
    '@context': 'http://schema.org',
    '@type': 'Restaurant',
    'name': 'Sua Pizzaria',
    'telephone': '(11) 99999-9999',
    // ... outros dados
}
```

## ✅ Checklist Final

Antes de colocar o site no ar, verifique:

- [ ] Todas as informações da pizzaria estão corretas
- [ ] Número do WhatsApp está funcionando
- [ ] Todas as imagens foram adicionadas
- [ ] Preços do cardápio estão atualizados
- [ ] Horários de funcionamento estão corretos
- [ ] Site está responsivo (teste em mobile)
- [ ] Formulário de contato funciona
- [ ] Carrinho de compras funciona corretamente
- [ ] Checkout completo funciona
- [ ] Links das redes sociais estão corretos
- [ ] Favicon está aparecendo
- [ ] Meta tags estão configuradas

## 🆘 Solução de Problemas

### Imagens não aparecem
- Verifique se os nomes dos arquivos estão corretos
- Confirme se as imagens estão na pasta `assets/images/`
- Verifique se não há espaços ou caracteres especiais nos nomes

### WhatsApp não abre
- Confirme se o número está no formato correto: `5511999999999`
- Teste o link manualmente: `https://wa.me/5511999999999`

### Site não carrega
- Verifique se todos os arquivos foram enviados
- Confirme se a estrutura de pastas está correta
- Teste em navegador diferente

### Carrinho não funciona
- Verifique se o JavaScript está habilitado
- Abra o console do navegador para ver erros
- Confirme se todos os arquivos JS foram carregados

## 📞 Suporte

Para dúvidas adicionais:
1. Consulte a documentação completa no `README.md`
2. Verifique os comentários no código
3. Teste em diferentes navegadores e dispositivos

---

**Boa sorte com sua pizzaria online! 🍕**
