<?php
/**
 * API de Upload - Laricas Delivery Admin
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar autenticação
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Não autenticado'], 401);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Método não permitido'], 405);
}

try {
    $uploadType = $_POST['type'] ?? 'product';
    $allowedTypes = ['product', 'category', 'banner', 'content'];
    
    if (!in_array($uploadType, $allowedTypes)) {
        jsonResponse(['success' => false, 'message' => 'Tipo de upload inválido'], 400);
    }
    
    if (!isset($_FILES['file'])) {
        jsonResponse(['success' => false, 'message' => 'Nenhum arquivo enviado'], 400);
    }
    
    $file = $_FILES['file'];
    
    // Definir diretório de upload baseado no tipo
    $uploadDirs = [
        'product' => '../uploads/products/',
        'category' => '../uploads/categories/',
        'banner' => '../uploads/banners/',
        'content' => '../uploads/content/'
    ];
    
    $uploadDir = $uploadDirs[$uploadType];
    
    // Criar diretório se não existir
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Tipos de arquivo permitidos
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    
    $result = uploadFile($file, $uploadDir, $allowedExtensions);
    
    if ($result['success']) {
        // Redimensionar imagem se necessário
        $sourcePath = $result['path'];
        $filename = $result['filename'];
        
        // Configurações de redimensionamento por tipo
        $resizeConfig = [
            'product' => ['width' => 800, 'height' => 600],
            'category' => ['width' => 400, 'height' => 300],
            'banner' => ['width' => 1200, 'height' => 600],
            'content' => ['width' => 1000, 'height' => 800]
        ];
        
        $config = $resizeConfig[$uploadType];
        
        // Criar versão redimensionada
        $resizedPath = $uploadDir . 'resized_' . $filename;
        if (resizeImage($sourcePath, $resizedPath, $config['width'], $config['height'])) {
            // Remover original e renomear redimensionada
            unlink($sourcePath);
            rename($resizedPath, $sourcePath);
        }
        
        // Criar thumbnail se for produto
        if ($uploadType === 'product') {
            $thumbPath = $uploadDir . 'thumb_' . $filename;
            resizeImage($sourcePath, $thumbPath, 200, 150);
        }
        
        logActivity('file_uploaded', null, null, null, [
            'type' => $uploadType,
            'filename' => $filename,
            'size' => $file['size']
        ]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Arquivo enviado com sucesso',
            'data' => [
                'filename' => $filename,
                'url' => str_replace('../', '', $result['path']),
                'thumbnail' => $uploadType === 'product' ? str_replace('../', '', $uploadDir . 'thumb_' . $filename) : null
            ]
        ]);
    } else {
        jsonResponse(['success' => false, 'message' => $result['message']], 400);
    }
    
} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Erro interno do servidor'], 500);
}
?>
