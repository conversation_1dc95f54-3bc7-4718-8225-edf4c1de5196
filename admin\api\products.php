<?php
/**
 * API de Produtos - Laricas Delivery Admin
 */

require_once '../includes/auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar autenticação
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Não autenticado'], 401);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDB();
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        case 'PUT':
            handlePut($db, $input);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Método não permitido'], 405);
    }
} catch (Exception $e) {
    error_log("Products API error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Erro interno do servidor'], 500);
}

function handleGet($db) {
    $id = $_GET['id'] ?? null;
    $category_id = $_GET['category_id'] ?? null;
    $status = $_GET['status'] ?? 'active';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(1, min(100, intval($_GET['limit'] ?? 20)));
    $search = $_GET['search'] ?? '';
    
    if ($id) {
        // Buscar produto específico
        $sql = "
            SELECT p.*, c.name as category_name,
                   (SELECT JSON_ARRAYAGG(
                       JSON_OBJECT('id', ps.id, 'size_name', ps.size_name, 
                                  'size_description', ps.size_description, 'price', ps.price, 
                                  'sort_order', ps.sort_order, 'status', ps.status)
                   ) FROM product_sizes ps WHERE ps.product_id = p.id AND ps.status = 'active' ORDER BY ps.sort_order) as sizes
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.id = :id
        ";
        
        $product = $db->fetch($sql, ['id' => $id]);
        
        if (!$product) {
            jsonResponse(['success' => false, 'message' => 'Produto não encontrado'], 404);
        }
        
        $product['sizes'] = json_decode($product['sizes'] ?? '[]', true) ?: [];
        $product['ingredients'] = json_decode($product['ingredients'] ?? '[]', true) ?: [];
        $product['allergens'] = json_decode($product['allergens'] ?? '[]', true) ?: [];
        $product['nutritional_info'] = json_decode($product['nutritional_info'] ?? '[]', true) ?: [];
        $product['gallery'] = json_decode($product['gallery'] ?? '[]', true) ?: [];
        
        jsonResponse(['success' => true, 'data' => $product]);
    } else {
        // Listar produtos
        $offset = ($page - 1) * $limit;
        $where = ['1=1'];
        $params = [];
        
        if ($category_id) {
            $where[] = 'p.category_id = :category_id';
            $params['category_id'] = $category_id;
        }
        
        if ($status !== 'all') {
            $where[] = 'p.status = :status';
            $params['status'] = $status;
        }
        
        if ($search) {
            $where[] = '(p.name LIKE :search OR p.description LIKE :search)';
            $params['search'] = "%{$search}%";
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Contar total
        $countSql = "SELECT COUNT(*) as total FROM products p WHERE {$whereClause}";
        $total = $db->fetch($countSql, $params)['total'];
        
        // Buscar produtos
        $sql = "
            SELECT p.*, c.name as category_name,
                   (SELECT COUNT(*) FROM product_sizes ps WHERE ps.product_id = p.id AND ps.status = 'active') as sizes_count,
                   (SELECT MIN(ps.price) FROM product_sizes ps WHERE ps.product_id = p.id AND ps.status = 'active') as min_price,
                   (SELECT MAX(ps.price) FROM product_sizes ps WHERE ps.product_id = p.id AND ps.status = 'active') as max_price
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE {$whereClause}
            ORDER BY p.sort_order ASC, p.created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $products = $db->fetchAll($sql, $params);
        
        $pagination = paginate($total, $limit, $page);
        
        jsonResponse([
            'success' => true,
            'data' => $products,
            'pagination' => $pagination
        ]);
    }
}

function handlePost($db, $input) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    // Validar dados obrigatórios
    $required = ['name', 'description', 'category_id'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            jsonResponse(['success' => false, 'message' => "Campo {$field} é obrigatório"], 400);
        }
    }
    
    // Verificar se categoria existe
    $category = $db->fetch("SELECT id FROM categories WHERE id = :id AND status = 'active'", 
                          ['id' => $input['category_id']]);
    if (!$category) {
        jsonResponse(['success' => false, 'message' => 'Categoria não encontrada'], 400);
    }
    
    $db->beginTransaction();
    
    try {
        // Gerar slug único
        $slug = generateSlug($input['name']);
        $originalSlug = $slug;
        $counter = 1;
        
        while ($db->fetch("SELECT id FROM products WHERE slug = :slug", ['slug' => $slug])) {
            $slug = $originalSlug . '-' . $counter++;
        }
        
        // Preparar dados do produto
        $productData = [
            'category_id' => $input['category_id'],
            'name' => sanitize($input['name']),
            'slug' => $slug,
            'description' => sanitize($input['description']),
            'short_description' => sanitize($input['short_description'] ?? ''),
            'image' => sanitize($input['image'] ?? ''),
            'is_popular' => !empty($input['is_popular']) ? 1 : 0,
            'is_promotion' => !empty($input['is_promotion']) ? 1 : 0,
            'promotion_text' => sanitize($input['promotion_text'] ?? ''),
            'original_price' => !empty($input['original_price']) ? floatval($input['original_price']) : null,
            'ingredients' => !empty($input['ingredients']) ? json_encode($input['ingredients']) : null,
            'allergens' => !empty($input['allergens']) ? json_encode($input['allergens']) : null,
            'nutritional_info' => !empty($input['nutritional_info']) ? json_encode($input['nutritional_info']) : null,
            'preparation_time' => !empty($input['preparation_time']) ? intval($input['preparation_time']) : null,
            'sort_order' => intval($input['sort_order'] ?? 0),
            'status' => $input['status'] ?? 'active'
        ];
        
        $productId = $db->insert('products', $productData);
        
        // Inserir tamanhos/preços
        if (!empty($input['sizes']) && is_array($input['sizes'])) {
            foreach ($input['sizes'] as $index => $size) {
                if (!empty($size['size_name']) && !empty($size['price'])) {
                    $sizeData = [
                        'product_id' => $productId,
                        'size_name' => sanitize($size['size_name']),
                        'size_description' => sanitize($size['size_description'] ?? ''),
                        'price' => floatval($size['price']),
                        'sort_order' => intval($size['sort_order'] ?? $index),
                        'status' => $size['status'] ?? 'active'
                    ];
                    
                    $db->insert('product_sizes', $sizeData);
                }
            }
        }
        
        $db->commit();
        
        logActivity('product_created', 'products', $productId, null, $productData);
        
        jsonResponse([
            'success' => true,
            'message' => 'Produto criado com sucesso',
            'data' => ['id' => $productId]
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

function handlePut($db, $input) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID do produto é obrigatório'], 400);
    }
    
    // Verificar se produto existe
    $product = $db->fetch("SELECT * FROM products WHERE id = :id", ['id' => $id]);
    if (!$product) {
        jsonResponse(['success' => false, 'message' => 'Produto não encontrado'], 404);
    }
    
    $db->beginTransaction();
    
    try {
        // Preparar dados para atualização
        $updateData = [];
        $allowedFields = [
            'category_id', 'name', 'description', 'short_description', 'image',
            'is_popular', 'is_promotion', 'promotion_text', 'original_price',
            'ingredients', 'allergens', 'nutritional_info', 'preparation_time',
            'sort_order', 'status'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                switch ($field) {
                    case 'is_popular':
                    case 'is_promotion':
                        $updateData[$field] = !empty($input[$field]) ? 1 : 0;
                        break;
                    case 'original_price':
                    case 'preparation_time':
                    case 'sort_order':
                        $updateData[$field] = !empty($input[$field]) ? floatval($input[$field]) : null;
                        break;
                    case 'ingredients':
                    case 'allergens':
                    case 'nutritional_info':
                        $updateData[$field] = !empty($input[$field]) ? json_encode($input[$field]) : null;
                        break;
                    default:
                        $updateData[$field] = sanitize($input[$field]);
                }
            }
        }
        
        // Atualizar slug se nome mudou
        if (isset($input['name']) && $input['name'] !== $product['name']) {
            $slug = generateSlug($input['name']);
            $originalSlug = $slug;
            $counter = 1;
            
            while ($db->fetch("SELECT id FROM products WHERE slug = :slug AND id != :id", 
                             ['slug' => $slug, 'id' => $id])) {
                $slug = $originalSlug . '-' . $counter++;
            }
            
            $updateData['slug'] = $slug;
        }
        
        if (!empty($updateData)) {
            $db->update('products', $updateData, 'id = :id', ['id' => $id]);
        }
        
        // Atualizar tamanhos se fornecidos
        if (isset($input['sizes']) && is_array($input['sizes'])) {
            // Remover tamanhos existentes
            $db->delete('product_sizes', 'product_id = :product_id', ['product_id' => $id]);
            
            // Inserir novos tamanhos
            foreach ($input['sizes'] as $index => $size) {
                if (!empty($size['size_name']) && !empty($size['price'])) {
                    $sizeData = [
                        'product_id' => $id,
                        'size_name' => sanitize($size['size_name']),
                        'size_description' => sanitize($size['size_description'] ?? ''),
                        'price' => floatval($size['price']),
                        'sort_order' => intval($size['sort_order'] ?? $index),
                        'status' => $size['status'] ?? 'active'
                    ];
                    
                    $db->insert('product_sizes', $sizeData);
                }
            }
        }
        
        $db->commit();
        
        logActivity('product_updated', 'products', $id, $product, $updateData);
        
        jsonResponse([
            'success' => true,
            'message' => 'Produto atualizado com sucesso'
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

function handleDelete($db) {
    if (!hasPermission('edit_products')) {
        jsonResponse(['success' => false, 'message' => 'Sem permissão'], 403);
    }
    
    $id = $_GET['id'] ?? null;
    if (!$id) {
        jsonResponse(['success' => false, 'message' => 'ID do produto é obrigatório'], 400);
    }
    
    // Verificar se produto existe
    $product = $db->fetch("SELECT * FROM products WHERE id = :id", ['id' => $id]);
    if (!$product) {
        jsonResponse(['success' => false, 'message' => 'Produto não encontrado'], 404);
    }
    
    // Verificar se produto está sendo usado em pedidos
    $orderCount = $db->fetch("SELECT COUNT(*) as count FROM order_items WHERE product_id = :id", 
                            ['id' => $id])['count'];
    
    if ($orderCount > 0) {
        // Não deletar, apenas inativar
        $db->update('products', ['status' => 'inactive'], 'id = :id', ['id' => $id]);
        $message = 'Produto inativado (estava sendo usado em pedidos)';
    } else {
        // Deletar produto e tamanhos
        $db->delete('product_sizes', 'product_id = :id', ['id' => $id]);
        $db->delete('products', 'id = :id', ['id' => $id]);
        $message = 'Produto deletado com sucesso';
    }
    
    logActivity('product_deleted', 'products', $id, $product, null);
    
    jsonResponse([
        'success' => true,
        'message' => $message
    ]);
}
?>
