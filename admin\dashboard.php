<?php
$pageTitle = 'Dashboard';
include 'includes/header.php';
?>

<div class="row">
    <!-- Stats Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Vendas Hoje
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-sales">
                            R$ 0,00
                        </div>
                        <div class="small text-muted">
                            <span id="sales-growth">0%</span> vs ontem
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Pedidos Hoje
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-orders">
                            0
                        </div>
                        <div class="small text-muted">
                            <span id="orders-growth">0%</span> vs ontem
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Ticket Médio
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="avg-order-value">
                            R$ 0,00
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pedidos Pendentes
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="pending-orders">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Sales Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Vendas dos Últimos 7 Dias</h6>
                <div class="dropdown no-arrow">
                    <select class="form-select form-select-sm" id="sales-period">
                        <option value="7days">7 dias</option>
                        <option value="30days">30 dias</option>
                        <option value="3months">3 meses</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders by Status -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Pedidos por Status</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Orders -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Pedidos Recentes</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="recent-orders-table">
                        <thead>
                            <tr>
                                <th>Pedido</th>
                                <th>Cliente</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Data</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Products -->
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Produtos Mais Vendidos</h6>
            </div>
            <div class="card-body">
                <div id="top-products">
                    <!-- Data will be loaded via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--accent-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--secondary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--accent-color) !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: var(--secondary-color) !important;
}

.chart-area {
    position: relative;
    height: 300px;
}

.chart-pie {
    position: relative;
    height: 250px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.product-item:last-child {
    border-bottom: none;
}

.product-image {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 0.75rem;
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.product-sales {
    font-size: 0.8rem;
    color: var(--text-light);
}

.product-value {
    font-weight: 600;
    color: var(--accent-color);
    font-size: 0.9rem;
}
</style>

<script>
let salesChart, statusChart;

// Load dashboard data
async function loadDashboardData() {
    try {
        // Load overview stats
        const overview = await apiRequest('dashboard.php?type=overview');
        updateOverviewStats(overview.data);
        
        // Load sales chart
        const sales = await apiRequest('dashboard.php?type=sales&period=7days');
        updateSalesChart(sales.data);
        
        // Load recent orders
        const orders = await apiRequest('dashboard.php?type=orders&limit=10');
        updateRecentOrders(orders.data.recent_orders);
        updateStatusChart(orders.data.orders_by_status);
        
        // Load top products
        const products = await apiRequest('dashboard.php?type=products&limit=5');
        updateTopProducts(products.data.top_products);
        
    } catch (error) {
        showError('Erro ao carregar dados do dashboard: ' + error.message);
    }
}

function updateOverviewStats(data) {
    document.getElementById('today-sales').textContent = formatCurrency(data.today.sales);
    document.getElementById('today-orders').textContent = data.today.orders;
    document.getElementById('avg-order-value').textContent = formatCurrency(data.today.avg_order_value);
    document.getElementById('pending-orders').textContent = data.totals.pending_orders;
    
    // Update growth indicators
    const salesGrowth = document.getElementById('sales-growth');
    const ordersGrowth = document.getElementById('orders-growth');
    
    salesGrowth.textContent = (data.today.sales_growth > 0 ? '+' : '') + data.today.sales_growth + '%';
    salesGrowth.className = data.today.sales_growth >= 0 ? 'text-success' : 'text-danger';
    
    ordersGrowth.textContent = (data.today.orders_growth > 0 ? '+' : '') + data.today.orders_growth + '%';
    ordersGrowth.className = data.today.orders_growth >= 0 ? 'text-success' : 'text-danger';
}

function updateSalesChart(data) {
    const ctx = document.getElementById('salesChart').getContext('2d');
    
    if (salesChart) {
        salesChart.destroy();
    }
    
    const labels = data.sales_by_day.map(item => {
        const date = new Date(item.date);
        return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
    });
    
    const salesData = data.sales_by_day.map(item => parseFloat(item.sales));
    const ordersData = data.sales_by_day.map(item => parseInt(item.orders));
    
    salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Vendas (R$)',
                data: salesData,
                borderColor: 'rgb(211, 47, 47)',
                backgroundColor: 'rgba(211, 47, 47, 0.1)',
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: 'Pedidos',
                data: ordersData,
                borderColor: 'rgb(76, 175, 80)',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Vendas (R$)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Pedidos'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function updateStatusChart(data) {
    const ctx = document.getElementById('statusChart').getContext('2d');
    
    if (statusChart) {
        statusChart.destroy();
    }
    
    const labels = data.map(item => {
        const statuses = {
            'pending': 'Pendente',
            'confirmed': 'Confirmado',
            'preparing': 'Em Preparo',
            'ready': 'Pronto',
            'out_for_delivery': 'Saiu para Entrega',
            'delivered': 'Entregue',
            'cancelled': 'Cancelado'
        };
        return statuses[item.status] || item.status;
    });
    
    const counts = data.map(item => parseInt(item.count));
    
    statusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: counts,
                backgroundColor: [
                    '#ffc107',
                    '#17a2b8',
                    '#007bff',
                    '#28a745',
                    '#20c997',
                    '#28a745',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updateRecentOrders(orders) {
    const tbody = document.querySelector('#recent-orders-table tbody');
    tbody.innerHTML = '';
    
    orders.forEach(order => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>#${order.order_number}</strong></td>
            <td>
                <div>${order.customer_name}</div>
                <small class="text-muted">${order.customer_phone}</small>
            </td>
            <td>${formatCurrency(order.total_amount)}</td>
            <td>${getStatusBadge(order.status)}</td>
            <td>${formatDate(order.created_at)}</td>
            <td>
                <a href="orders.php?id=${order.id}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i>
                </a>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateTopProducts(products) {
    const container = document.getElementById('top-products');
    container.innerHTML = '';
    
    products.forEach(product => {
        const item = document.createElement('div');
        item.className = 'product-item';
        item.innerHTML = `
            <img src="../assets/images/${product.image || 'placeholder.jpg'}" 
                 alt="${product.name}" 
                 class="product-image"
                 onerror="this.src='../assets/images/placeholder.jpg'">
            <div class="product-info">
                <div class="product-name">${product.name}</div>
                <div class="product-sales">${product.total_quantity} vendidos</div>
            </div>
            <div class="product-value">${formatCurrency(product.total_sales)}</div>
        `;
        container.appendChild(item);
    });
}

// Sales period change handler
document.getElementById('sales-period').addEventListener('change', async function() {
    const period = this.value;
    try {
        const sales = await apiRequest(`dashboard.php?type=sales&period=${period}`);
        updateSalesChart(sales.data);
    } catch (error) {
        showError('Erro ao carregar dados de vendas: ' + error.message);
    }
});

// Load data on page load
document.addEventListener('DOMContentLoaded', loadDashboardData);

// Auto-refresh every 5 minutes
setInterval(loadDashboardData, 5 * 60 * 1000);
</script>

<?php include 'includes/footer.php'; ?>
