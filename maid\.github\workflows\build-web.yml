name: Build Web

on:
  workflow_call:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/build-web.yml'
      - 'web/**'
      - 'lib/**'
      - '*.yaml'

permissions:
  contents: write 

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.0'

      - name: Build Web App
        run: |
          flutter pub get
          flutter build web -v

      - name: Add CNAME file
        run: echo 'maid-app.com' > ./build/web/CNAME

      - name: Upload Web Build
        uses: actions/upload-artifact@v4
        with:
          name: maid-web
          path: build/web

      - name: Deploy to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          branch: gh-pages
          folder: build/web
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}