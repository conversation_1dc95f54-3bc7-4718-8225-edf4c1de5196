# Laricas Delivery - Site de Pizzaria

Um site moderno e responsivo para a pizzaria Laricas Delivery, desenvolvido com HTML5, CSS3 e JavaScript vanilla. O site oferece uma experiência completa de pedidos online com integração ao WhatsApp.

## 🍕 Características Principais

### Design e UX
- **Design Moderno**: Interface limpa e intuitiva inspirada no site original
- **Totalmente Responsivo**: Adaptação perfeita para desktop, tablet e mobile
- **Performance Otimizada**: Carregamento rápido e navegação fluida
- **Acessibilidade**: Seguindo boas práticas de acessibilidade web

### Funcionalidades
- **Cardápio Interativo**: Navegação por categorias com filtros
- **Carrinho de Compras**: Sistema completo com persistência local
- **Checkout Inteligente**: Formulário com validação e busca de CEP
- **Integração WhatsApp**: Finalização de pedidos via WhatsApp
- **Sistema de Promoções**: Destaque para ofertas especiais
- **Formulário de Contato**: Comunicação direta com a pizzaria

## 📁 Estrutura do Projeto

```
laricas-delivery/
├── index.html              # Página principal
├── checkout.html           # Página de finalização
├── README.md              # Documentação
├── assets/
│   ├── css/
│   │   ├── style.css      # Estilos principais
│   │   ├── responsive.css # Estilos responsivos
│   │   └── checkout.css   # Estilos do checkout
│   ├── js/
│   │   ├── main.js        # JavaScript principal
│   │   ├── menu-data.js   # Dados do cardápio
│   │   ├── cart.js        # Sistema de carrinho
│   │   └── checkout.js    # Lógica do checkout
│   └── images/
│       └── README.md      # Guia de imagens
```

## 🚀 Como Usar

### 1. Configuração Inicial
1. Faça o download ou clone do projeto
2. Adicione as imagens necessárias na pasta `assets/images/`
3. Configure o número do WhatsApp nos arquivos JavaScript
4. Personalize as informações da pizzaria

### 2. Personalização

#### Dados da Pizzaria
Edite os seguintes arquivos para personalizar:

**Informações de Contato** (em todos os arquivos HTML):
- Telefone: `(14) 99999-9999`
- Horários de funcionamento
- Endereço (se aplicável)

**Cardápio** (`assets/js/menu-data.js`):
- Adicione, remova ou edite produtos
- Ajuste preços e descrições
- Configure promoções

#### Cores e Visual
No arquivo `assets/css/style.css`, ajuste as variáveis CSS:
```css
:root {
    --primary-color: #d32f2f;    /* Cor principal */
    --secondary-color: #ff6f00;  /* Cor secundária */
    --accent-color: #4caf50;     /* Cor de destaque */
}
```

### 3. Imagens Necessárias
Consulte `assets/images/README.md` para a lista completa de imagens necessárias e suas especificações.

## 📱 Funcionalidades Detalhadas

### Sistema de Cardápio
- **Categorias**: Pizzas Salgadas, Pizzas Doces, Esfihas, Bebidas, Promoções
- **Filtros**: Navegação por categoria com botões interativos
- **Produtos**: Cada item possui nome, descrição, imagem, tamanhos e preços
- **Badges**: Indicadores de "Popular", "Mais Vendida", etc.

### Carrinho de Compras
- **Persistência**: Dados salvos no localStorage
- **Quantidades**: Controle de quantidade por item
- **Tamanhos**: Seleção de tamanhos diferentes
- **Total**: Cálculo automático com taxa de entrega
- **Modal**: Interface limpa para visualização

### Checkout
- **Dados Pessoais**: Nome, telefone, e-mail
- **Endereço**: Busca automática por CEP via API ViaCEP
- **Pagamento**: Opções de dinheiro, cartão e PIX
- **Validação**: Verificação completa dos dados
- **WhatsApp**: Envio automático do pedido formatado

### Integração WhatsApp
O sistema gera mensagens formatadas contendo:
- Dados do cliente
- Endereço completo de entrega
- Itens do pedido com quantidades e preços
- Total do pedido
- Forma de pagamento
- Observações especiais

## 🛠 Tecnologias Utilizadas

- **HTML5**: Estrutura semântica e acessível
- **CSS3**: 
  - Flexbox e Grid Layout
  - Custom Properties (variáveis CSS)
  - Media Queries para responsividade
  - Animações e transições
- **JavaScript ES6+**:
  - Classes e módulos
  - LocalStorage API
  - Fetch API para busca de CEP
  - Event Listeners e DOM manipulation

## 📊 APIs Utilizadas

- **ViaCEP**: Busca automática de endereços por CEP
- **WhatsApp Business**: Integração para envio de pedidos

## 🎨 Design System

### Cores
- **Primária**: #d32f2f (Vermelho)
- **Secundária**: #ff6f00 (Laranja)
- **Sucesso**: #4caf50 (Verde)
- **Texto**: #212121 (Cinza escuro)
- **Texto Claro**: #757575 (Cinza médio)

### Tipografia
- **Fonte**: Poppins (Google Fonts)
- **Pesos**: 300, 400, 500, 600, 700

### Espaçamentos
Sistema baseado em múltiplos de 4px:
- XS: 4px, SM: 8px, MD: 16px, LG: 24px, XL: 32px

## 📱 Responsividade

### Breakpoints
- **Mobile**: até 480px
- **Tablet**: 481px - 768px
- **Desktop**: 769px - 1024px
- **Large Desktop**: 1025px+

### Adaptações Mobile
- Menu hambúrguer
- Cards em coluna única
- Formulários simplificados
- Botões de toque otimizados

## 🔧 Manutenção

### Atualizando o Cardápio
1. Edite `assets/js/menu-data.js`
2. Adicione novas imagens em `assets/images/`
3. Teste todas as funcionalidades

### Alterando Informações
- **Telefone**: Busque por `5514999999999` nos arquivos JS
- **Horários**: Edite nos arquivos HTML e schema.org
- **Endereço**: Atualize nas seções de contato

## 🚀 Deploy

### Hospedagem Estática
O site pode ser hospedado em qualquer servidor web estático:
- GitHub Pages
- Netlify
- Vercel
- Servidor próprio

### Configurações do Servidor
- Não requer backend
- Apenas arquivos estáticos
- HTTPS recomendado para melhor SEO

## 📈 SEO e Performance

### Otimizações Incluídas
- Meta tags otimizadas
- Schema.org markup
- Imagens com alt text
- Sitemap (recomendado adicionar)
- Compressão de imagens

### Métricas de Performance
- First Contentful Paint otimizado
- Largest Contentful Paint < 2.5s
- Cumulative Layout Shift minimizado

## 🤝 Suporte

Para dúvidas ou suporte:
1. Consulte a documentação
2. Verifique os comentários no código
3. Teste em diferentes dispositivos
4. Valide HTML e CSS

## 📄 Licença

Este projeto foi desenvolvido para uso comercial da Laricas Delivery. Todos os direitos reservados.

---

**Desenvolvido com ❤️ para a melhor experiência de pedidos online**
