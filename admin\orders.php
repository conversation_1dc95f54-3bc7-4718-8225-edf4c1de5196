<?php
$pageTitle = 'Gerenciar Pedidos';
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">Pedidos</h1>
    <div class="d-flex gap-2">
        <select class="form-select" id="status-filter">
            <option value="">Todos os Status</option>
            <option value="pending">Pendente</option>
            <option value="confirmed">Confirmado</option>
            <option value="preparing">Em Preparo</option>
            <option value="ready">Pronto</option>
            <option value="out_for_delivery">Saiu para Entrega</option>
            <option value="delivered">Entregue</option>
            <option value="cancelled">Cancelado</option>
        </select>
        <input type="date" class="form-control" id="date-filter" style="max-width: 200px;">
        <button class="btn btn-primary" onclick="refreshOrders()">
            <i class="fas fa-sync-alt"></i> Atualizar
        </button>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h5 mb-0" id="pending-count">0</div>
                        <div class="small">Pendentes</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h5 mb-0" id="preparing-count">0</div>
                        <div class="small">Em Preparo</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-utensils fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h5 mb-0" id="ready-count">0</div>
                        <div class="small">Prontos</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h5 mb-0" id="delivered-count">0</div>
                        <div class="small">Entregues Hoje</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Lista de Pedidos</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="orders-table">
                <thead>
                    <tr>
                        <th>Pedido</th>
                        <th>Cliente</th>
                        <th>Itens</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Data</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detalhes do Pedido</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="order-details">
                <!-- Order details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <div id="order-actions">
                    <!-- Action buttons will be added here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Atualizar Status do Pedido</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="status-form">
                    <input type="hidden" id="order-id">
                    <div class="mb-3">
                        <label for="new-status" class="form-label">Novo Status</label>
                        <select class="form-select" id="new-status" required>
                            <option value="">Selecione o status</option>
                            <option value="confirmed">Confirmado</option>
                            <option value="preparing">Em Preparo</option>
                            <option value="ready">Pronto</option>
                            <option value="out_for_delivery">Saiu para Entrega</option>
                            <option value="delivered">Entregue</option>
                            <option value="cancelled">Cancelado</option>
                        </select>
                    </div>
                    <div class="mb-3" id="cancellation-reason-group" style="display: none;">
                        <label for="cancellation-reason" class="form-label">Motivo do Cancelamento</label>
                        <textarea class="form-control" id="cancellation-reason" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="estimated-time" class="form-label">Tempo Estimado de Entrega</label>
                        <input type="datetime-local" class="form-control" id="estimated-time">
                    </div>
                    <div class="mb-3">
                        <label for="order-notes" class="form-label">Observações</label>
                        <textarea class="form-control" id="order-notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="updateOrderStatus()">Atualizar Status</button>
            </div>
        </div>
    </div>
</div>

<style>
.order-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 0.75rem;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.item-info {
    font-size: 0.8rem;
    color: var(--text-light);
}

.item-price {
    font-weight: 600;
    color: var(--primary-color);
}

.address-info {
    background-color: var(--background-light);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.payment-info {
    background-color: var(--background-light);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.status-timeline {
    display: flex;
    justify-content: space-between;
    margin: 1rem 0;
    position: relative;
}

.status-timeline::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--border-color);
    z-index: 1;
}

.timeline-step {
    background-color: white;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    font-size: 0.8rem;
}

.timeline-step.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.timeline-step.completed {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}
</style>

<script>
let ordersTable;
let currentOrderId = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeOrdersTable();
    loadOrderStats();
    
    // Set up filters
    document.getElementById('status-filter').addEventListener('change', refreshOrders);
    document.getElementById('date-filter').addEventListener('change', refreshOrders);
    
    // Status change handler
    document.getElementById('new-status').addEventListener('change', function() {
        const cancellationGroup = document.getElementById('cancellation-reason-group');
        if (this.value === 'cancelled') {
            cancellationGroup.style.display = 'block';
        } else {
            cancellationGroup.style.display = 'none';
        }
    });
});

function initializeOrdersTable() {
    ordersTable = initDataTable('#orders-table', {
        ajax: {
            url: 'api/orders.php',
            data: function(d) {
                d.status = document.getElementById('status-filter').value;
                d.date_from = document.getElementById('date-filter').value;
                d.date_to = document.getElementById('date-filter').value;
            },
            dataSrc: 'data'
        },
        columns: [
            {
                data: 'order_number',
                render: function(data, type, row) {
                    return `<strong>#${data}</strong>`;
                }
            },
            {
                data: null,
                render: function(data, type, row) {
                    return `
                        <div>${row.customer_name}</div>
                        <small class="text-muted">${row.customer_phone}</small>
                    `;
                }
            },
            {
                data: 'items_count',
                render: function(data) {
                    return `${data} ${data === 1 ? 'item' : 'itens'}`;
                }
            },
            {
                data: 'total_amount',
                render: function(data) {
                    return formatCurrency(data);
                }
            },
            {
                data: 'status',
                render: function(data) {
                    return getStatusBadge(data);
                }
            },
            {
                data: 'created_at',
                render: function(data) {
                    return formatDate(data);
                }
            },
            {
                data: null,
                orderable: false,
                render: function(data, type, row) {
                    return `
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewOrder(${row.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="updateStatus(${row.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[5, 'desc']] // Order by date descending
    });
}

async function loadOrderStats() {
    try {
        const response = await apiRequest('dashboard.php?type=orders');
        const statusCounts = response.data.orders_by_status;
        
        // Reset counts
        document.getElementById('pending-count').textContent = '0';
        document.getElementById('preparing-count').textContent = '0';
        document.getElementById('ready-count').textContent = '0';
        document.getElementById('delivered-count').textContent = '0';
        
        // Update counts
        statusCounts.forEach(item => {
            const element = document.getElementById(item.status + '-count');
            if (element) {
                element.textContent = item.count;
            }
        });
        
    } catch (error) {
        console.error('Error loading order stats:', error);
    }
}

function refreshOrders() {
    if (ordersTable) {
        ordersTable.ajax.reload();
    }
    loadOrderStats();
}

async function viewOrder(orderId) {
    try {
        showLoading('#order-details');
        
        const response = await apiRequest(`orders.php?id=${orderId}`);
        const order = response.data;
        
        displayOrderDetails(order);
        
        const modal = new bootstrap.Modal(document.getElementById('orderModal'));
        modal.show();
        
    } catch (error) {
        showError('Erro ao carregar detalhes do pedido: ' + error.message);
    }
}

function displayOrderDetails(order) {
    const container = document.getElementById('order-details');
    const actions = document.getElementById('order-actions');
    
    // Order items
    const itemsHtml = order.items.map(item => `
        <div class="order-item">
            <div class="item-details">
                <div class="item-name">${item.product_name}</div>
                <div class="item-info">
                    ${item.product_size ? `Tamanho: ${item.product_size} | ` : ''}
                    Qtd: ${item.quantity} x ${formatCurrency(item.unit_price)}
                </div>
            </div>
            <div class="item-price">${formatCurrency(item.total_price)}</div>
        </div>
    `).join('');
    
    // Address
    const address = order.delivery_address;
    const addressHtml = `
        <div class="address-info">
            <h6><i class="fas fa-map-marker-alt me-2"></i>Endereço de Entrega</h6>
            <p class="mb-1">${address.street}, ${address.number}${address.complement ? ' - ' + address.complement : ''}</p>
            <p class="mb-1">${address.neighborhood} - ${address.city}</p>
            <p class="mb-0">CEP: ${address.zip_code}</p>
            ${address.reference ? `<p class="mb-0 text-muted">Ref: ${address.reference}</p>` : ''}
        </div>
    `;
    
    // Payment
    const paymentMethods = {
        'money': 'Dinheiro',
        'card': 'Cartão na Entrega',
        'pix': 'PIX',
        'online': 'Pagamento Online'
    };
    
    const paymentHtml = `
        <div class="payment-info">
            <h6><i class="fas fa-credit-card me-2"></i>Pagamento</h6>
            <p class="mb-1">Método: ${paymentMethods[order.payment_method] || order.payment_method}</p>
            ${order.payment_details && order.payment_details.changeFor ? 
                `<p class="mb-0">Troco para: ${formatCurrency(order.payment_details.changeFor)}</p>` : ''}
        </div>
    `;
    
    container.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Pedido #${order.order_number}</h6>
                <p class="text-muted">Realizado em ${formatDate(order.created_at)}</p>
                <p><strong>Status:</strong> ${getStatusBadge(order.status)}</p>
                ${order.estimated_delivery_time ? `<p><strong>Previsão:</strong> ${formatDate(order.estimated_delivery_time)}</p>` : ''}
            </div>
            <div class="col-md-6">
                <h6>Cliente</h6>
                <p class="mb-1">${order.customer_name}</p>
                <p class="mb-1">${order.customer_phone}</p>
                ${order.customer_email ? `<p class="mb-0">${order.customer_email}</p>` : ''}
            </div>
        </div>
        
        ${addressHtml}
        ${paymentHtml}
        
        <h6 class="mt-3">Itens do Pedido</h6>
        <div class="order-items">
            ${itemsHtml}
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                ${order.notes ? `
                    <h6>Observações</h6>
                    <p class="text-muted">${order.notes}</p>
                ` : ''}
            </div>
            <div class="col-md-6">
                <div class="text-end">
                    <p class="mb-1">Subtotal: ${formatCurrency(order.subtotal)}</p>
                    <p class="mb-1">Taxa de Entrega: ${order.delivery_fee > 0 ? formatCurrency(order.delivery_fee) : 'Grátis'}</p>
                    ${order.discount_amount > 0 ? `<p class="mb-1">Desconto: -${formatCurrency(order.discount_amount)}</p>` : ''}
                    <h5 class="mb-0">Total: ${formatCurrency(order.total_amount)}</h5>
                </div>
            </div>
        </div>
    `;
    
    // Action buttons
    if (order.status !== 'delivered' && order.status !== 'cancelled') {
        actions.innerHTML = `
            <button type="button" class="btn btn-primary" onclick="updateStatus(${order.id})">
                <i class="fas fa-edit me-2"></i>Atualizar Status
            </button>
        `;
    } else {
        actions.innerHTML = '';
    }
}

function updateStatus(orderId) {
    currentOrderId = orderId;
    
    // Reset form
    document.getElementById('status-form').reset();
    document.getElementById('order-id').value = orderId;
    document.getElementById('cancellation-reason-group').style.display = 'none';
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

async function updateOrderStatus() {
    if (!currentOrderId) return;
    
    const status = document.getElementById('new-status').value;
    const notes = document.getElementById('order-notes').value;
    const estimatedTime = document.getElementById('estimated-time').value;
    const cancellationReason = document.getElementById('cancellation-reason').value;
    
    if (!status) {
        showError('Por favor, selecione um status');
        return;
    }
    
    try {
        const data = {
            status: status,
            notes: notes,
            estimated_delivery_time: estimatedTime || null
        };
        
        if (status === 'cancelled' && cancellationReason) {
            data.cancellation_reason = cancellationReason;
        }
        
        await apiRequest(`orders.php?id=${currentOrderId}`, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
        
        showSuccess('Status do pedido atualizado com sucesso');
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('statusModal')).hide();
        
        // Refresh table
        refreshOrders();
        
        // Close order details modal if open
        const orderModal = bootstrap.Modal.getInstance(document.getElementById('orderModal'));
        if (orderModal) {
            orderModal.hide();
        }
        
    } catch (error) {
        showError('Erro ao atualizar status: ' + error.message);
    }
}

// Auto-refresh every 30 seconds
setInterval(refreshOrders, 30000);
</script>

<?php include 'includes/footer.php'; ?>
