<?php
/**
 * Funções Auxiliares - Laricas Delivery Admin
 */

/**
 * Sanitizar entrada de dados
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validar email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validar telefone brasileiro
 */
function isValidPhone($phone) {
    $phone = preg_replace('/\D/', '', $phone);
    return preg_match('/^(\d{10}|\d{11})$/', $phone);
}

/**
 * Formatar preço para exibição
 */
function formatPrice($price) {
    return 'R$ ' . number_format($price, 2, ',', '.');
}

/**
 * Formatar data para exibição
 */
function formatDate($date, $format = 'd/m/Y H:i') {
    if (empty($date)) return '-';
    return date($format, strtotime($date));
}

/**
 * Formatar telefone para exibição
 */
function formatPhone($phone) {
    $phone = preg_replace('/\D/', '', $phone);
    if (strlen($phone) == 11) {
        return preg_replace('/(\d{2})(\d{5})(\d{4})/', '($1) $2-$3', $phone);
    } elseif (strlen($phone) == 10) {
        return preg_replace('/(\d{2})(\d{4})(\d{4})/', '($1) $2-$3', $phone);
    }
    return $phone;
}

/**
 * Gerar slug a partir de string
 */
function generateSlug($string) {
    $string = strtolower($string);
    $string = preg_replace('/[áàâãä]/u', 'a', $string);
    $string = preg_replace('/[éèêë]/u', 'e', $string);
    $string = preg_replace('/[íìîï]/u', 'i', $string);
    $string = preg_replace('/[óòôõö]/u', 'o', $string);
    $string = preg_replace('/[úùûü]/u', 'u', $string);
    $string = preg_replace('/[ç]/u', 'c', $string);
    $string = preg_replace('/[^a-z0-9\s-]/', '', $string);
    $string = preg_replace('/[\s-]+/', '-', $string);
    return trim($string, '-');
}

/**
 * Upload de arquivo
 */
function uploadFile($file, $uploadDir = 'uploads/', $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'Nenhum arquivo enviado'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Erro no upload do arquivo'];
    }
    
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    // Verificar tipo de arquivo
    if (!in_array($fileExt, $allowedTypes)) {
        return ['success' => false, 'message' => 'Tipo de arquivo não permitido'];
    }
    
    // Verificar tamanho (5MB máximo)
    if ($fileSize > 5 * 1024 * 1024) {
        return ['success' => false, 'message' => 'Arquivo muito grande (máximo 5MB)'];
    }
    
    // Gerar nome único
    $newFileName = uniqid() . '_' . time() . '.' . $fileExt;
    $uploadPath = $uploadDir . $newFileName;
    
    // Criar diretório se não existir
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Mover arquivo
    if (move_uploaded_file($fileTmp, $uploadPath)) {
        return [
            'success' => true,
            'message' => 'Arquivo enviado com sucesso',
            'filename' => $newFileName,
            'path' => $uploadPath
        ];
    } else {
        return ['success' => false, 'message' => 'Erro ao salvar arquivo'];
    }
}

/**
 * Redimensionar imagem
 */
function resizeImage($sourcePath, $destPath, $maxWidth = 800, $maxHeight = 600, $quality = 85) {
    if (!file_exists($sourcePath)) {
        return false;
    }
    
    $imageInfo = getimagesize($sourcePath);
    if (!$imageInfo) {
        return false;
    }
    
    $sourceWidth = $imageInfo[0];
    $sourceHeight = $imageInfo[1];
    $sourceType = $imageInfo[2];
    
    // Calcular novas dimensões
    $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
    $newWidth = intval($sourceWidth * $ratio);
    $newHeight = intval($sourceHeight * $ratio);
    
    // Criar imagem de origem
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($sourcePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($sourcePath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }
    
    // Criar imagem de destino
    $destImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preservar transparência para PNG e GIF
    if ($sourceType == IMAGETYPE_PNG || $sourceType == IMAGETYPE_GIF) {
        imagealphablending($destImage, false);
        imagesavealpha($destImage, true);
        $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
        imagefilledrectangle($destImage, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Redimensionar
    imagecopyresampled($destImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);
    
    // Salvar imagem
    $result = false;
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($destImage, $destPath, $quality);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($destImage, $destPath);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($destImage, $destPath);
            break;
    }
    
    // Limpar memória
    imagedestroy($sourceImage);
    imagedestroy($destImage);
    
    return $result;
}

/**
 * Gerar número de pedido único
 */
function generateOrderNumber() {
    return 'LD' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * Calcular distância entre dois pontos (CEPs)
 */
function calculateDistance($cep1, $cep2) {
    // Implementação simplificada - em produção usar API de geolocalização
    return rand(1, 15); // Retorna distância aleatória entre 1-15km
}

/**
 * Verificar se CEP está na área de entrega
 */
function isInDeliveryArea($cep) {
    // Implementação simplificada - em produção verificar contra lista de CEPs
    $cep = preg_replace('/\D/', '', $cep);
    return strlen($cep) == 8 && substr($cep, 0, 5) == '17500'; // CEPs de Marília
}

/**
 * Enviar notificação por email (simulado)
 */
function sendEmailNotification($to, $subject, $message, $type = 'order') {
    // Em produção, implementar envio real de email
    error_log("Email notification: TO={$to}, SUBJECT={$subject}, TYPE={$type}");
    return true;
}

/**
 * Enviar notificação por WhatsApp (simulado)
 */
function sendWhatsAppNotification($phone, $message) {
    // Em produção, implementar integração com API do WhatsApp Business
    error_log("WhatsApp notification: PHONE={$phone}, MESSAGE=" . substr($message, 0, 100));
    return true;
}

/**
 * Obter configuração do sistema
 */
function getSetting($key, $default = null) {
    static $settings = null;
    
    if ($settings === null) {
        try {
            $db = getDB();
            $result = $db->fetchAll("SELECT `key`, `value`, `type` FROM settings");
            $settings = [];
            foreach ($result as $row) {
                $value = $row['value'];
                switch ($row['type']) {
                    case 'number':
                        $value = is_numeric($value) ? (float)$value : 0;
                        break;
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'json':
                        $value = json_decode($value, true) ?: [];
                        break;
                }
                $settings[$row['key']] = $value;
            }
        } catch (Exception $e) {
            $settings = [];
        }
    }
    
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * Salvar configuração do sistema
 */
function setSetting($key, $value, $type = 'string') {
    try {
        $db = getDB();
        
        if ($type === 'json') {
            $value = json_encode($value);
        } elseif ($type === 'boolean') {
            $value = $value ? 'true' : 'false';
        }
        
        $existing = $db->fetch("SELECT id FROM settings WHERE `key` = :key", ['key' => $key]);
        
        if ($existing) {
            $db->update('settings', 
                ['value' => $value, 'type' => $type], 
                '`key` = :key', 
                ['key' => $key]
            );
        } else {
            $db->insert('settings', [
                'key' => $key,
                'value' => $value,
                'type' => $type
            ]);
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Set setting error: " . $e->getMessage());
        return false;
    }
}

/**
 * Verificar se a pizzaria está aberta
 */
function isOpen() {
    $hours = getSetting('opening_hours', []);
    if (empty($hours)) return true;
    
    $currentDay = strtolower(date('l'));
    $currentTime = date('H:i');
    
    $dayMap = [
        'monday' => 'monday',
        'tuesday' => 'tuesday', 
        'wednesday' => 'wednesday',
        'thursday' => 'thursday',
        'friday' => 'friday',
        'saturday' => 'saturday',
        'sunday' => 'sunday'
    ];
    
    $day = $dayMap[$currentDay] ?? null;
    if (!$day || !isset($hours[$day])) return true;
    
    $dayHours = $hours[$day];
    if ($dayHours['closed']) return false;
    
    $openTime = $dayHours['open'];
    $closeTime = $dayHours['close'];
    
    // Verificar se está no horário
    if ($closeTime < $openTime) {
        // Horário que passa da meia-noite
        return $currentTime >= $openTime || $currentTime <= $closeTime;
    } else {
        return $currentTime >= $openTime && $currentTime <= $closeTime;
    }
}

/**
 * Obter status de pedido em português
 */
function getOrderStatusText($status) {
    $statuses = [
        'pending' => 'Pendente',
        'confirmed' => 'Confirmado',
        'preparing' => 'Em Preparo',
        'ready' => 'Pronto',
        'out_for_delivery' => 'Saiu para Entrega',
        'delivered' => 'Entregue',
        'cancelled' => 'Cancelado'
    ];
    
    return $statuses[$status] ?? $status;
}

/**
 * Obter cor do status do pedido
 */
function getOrderStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'confirmed' => 'info',
        'preparing' => 'primary',
        'ready' => 'success',
        'out_for_delivery' => 'info',
        'delivered' => 'success',
        'cancelled' => 'danger'
    ];
    
    return $colors[$status] ?? 'secondary';
}

/**
 * Gerar token CSRF
 */
function generateCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Verificar token CSRF
 */
function verifyCSRFToken($token) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Resposta JSON padronizada
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Paginação
 */
function paginate($totalItems, $itemsPerPage = 20, $currentPage = 1) {
    $totalPages = ceil($totalItems / $itemsPerPage);
    $currentPage = max(1, min($totalPages, $currentPage));
    $offset = ($currentPage - 1) * $itemsPerPage;
    
    return [
        'total_items' => $totalItems,
        'items_per_page' => $itemsPerPage,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages
    ];
}
?>
